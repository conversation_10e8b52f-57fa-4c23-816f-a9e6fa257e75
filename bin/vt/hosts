#!/bin/bash

# Unified hosts management script that delegates to VM-specific scripts

# shellcheck disable=SC1090
source ~/.ilm/share/utils

set -euo pipefail

usage() {
    cat <<EOF
Usage: $0 <vm-type> <command> [options]

Unified script to manage /etc/hosts entries for different VM types.

VM TYPES:
    vm          Manage libvirt VMs (using vm-hosts)
    incus       Manage Incus VMs (using ivm-hosts)
    multipass   Manage Multipass VMs (using mp-vm-hosts)

COMMANDS:
    add <vm-name>           Add VM to /etc/hosts (auto-detect IP)
    remove <vm-name>        Remove VM from /etc/hosts
    list                    List all VM entries in /etc/hosts
    update                  Update all running VMs in /etc/hosts
    clean                   Remove entries for non-existent VMs

EXAMPLES:
    $0 vm add ubuntu-vm        # Add libvirt VM to /etc/hosts
    $0 incus remove old-vm     # Remove Incus VM from /etc/hosts
    $0 multipass update        # Update all running Multipass VMs
    $0 vm list                 # Show all libvirt VM entries

EOF
}

check_script_exists() {
    local script="$1"
    if ! has_cmd "$script"; then
        fail "Required script '$script' not found in PATH"
        fail "Make sure it exists and is executable"
        exit 1
    fi
}

if [[ $# -lt 1 ]]; then
    usage
    exit 1
fi

vm_type="$1"
shift

case "$vm_type" in
vm)
    script="vm-hosts"
    check_script_exists "$script"
    "$script" "$@"
    ;;
incus)
    script="ivm-hosts"
    check_script_exists "$script"
    "$script" "$@"
    ;;
multipass)
    script="mp-vm-hosts"
    check_script_exists "$script"
    "$script" "$@"
    ;;
--help | -h)
    usage
    ;;
*)
    fail "Unknown VM type: $vm_type"
    usage
    exit 1
    ;;
esac
