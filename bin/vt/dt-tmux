#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

UBUNTU_CT="ubuntu"
FEDORA_CT="fedora"
ARCH_CT="arch"
DEBIAN_CT="debian"
ALPINE_CT="alpine"
NIX_CT="nix"

SESSION_NAME="distrobox-containers"

usage() {
    cat <<EOF
Usage: $(basename "$0") [OPTION]

Manage a tmux session with connections to distrobox containers.

Options:
  create    Create a new tmux session with connections to containers (default if no option)
  attach    Attach to an existing session
  detach    Detach from the current session
  destroy   Kill the tmux session
  help      Display this help message

Examples:
  $(basename "$0")           # Create session or attach if exists
  $(basename "$0") create    # Force create a new session
  $(basename "$0") attach    # Attach to existing session
  $(basename "$0") detach    # Detach from current session
  $(basename "$0") destroy   # Kill the session
EOF
}

check_distrobox() {
    if ! has_cmd distrobox; then
        fail "distrobox is not installed. Please install it first."
        exit 1
    fi
}

check_tmux() {
    if ! has_cmd tmux; then
        fail "tmux is not installed. Please install it first."
        exit 1
    fi
}

container_exists() {
    local container_name="$1"
    distrobox list | grep -q "^$container_name "
    return $?
}

create_container_cmd() {
    local container_name="$1"

    if ! container_exists "$container_name"; then
        echo "echo 'Container $container_name does not exist. Create it with: dt create $container_name $container_name'; sleep infinity"
        return
    fi

    if ! distrobox list | grep -q "^$container_name.*Up"; then
        echo "echo 'Container $container_name is not running. Starting it...'; distrobox enter $container_name"
    else
        echo "distrobox enter $container_name"
    fi
}

create_session() {
    local force="${1:-false}"

    if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
        if [[ "$force" == "true" ]]; then
            slog "Killing existing session and creating a new one..."
            tmux kill-session -t "$SESSION_NAME"
        else
            slog "Session '$SESSION_NAME' already exists, attaching..."
            tmux attach-session -t "$SESSION_NAME"
            return
        fi
    fi

    slog "Creating tmux session with connections to 6 distrobox containers..."

    UBUNTU_CMD=$(create_container_cmd "$UBUNTU_CT")
    FEDORA_CMD=$(create_container_cmd "$FEDORA_CT")
    ARCH_CMD=$(create_container_cmd "$ARCH_CT")
    DEBIAN_CMD=$(create_container_cmd "$DEBIAN_CT")
    ALPINE_CMD=$(create_container_cmd "$ALPINE_CT")
    NIX_CMD=$(create_container_cmd "$NIX_CT")

    # Create a new session with the first container
    tmux new-session -d -s "$SESSION_NAME" -n "Containers" "$UBUNTU_CMD"

    # Split the window for the remaining containers
    tmux split-window -h -t "$SESSION_NAME:0.0" "$FEDORA_CMD"
    tmux split-window -v -t "$SESSION_NAME:0.0" "$ARCH_CMD"
    tmux split-window -v -t "$SESSION_NAME:0.1" "$DEBIAN_CMD"

    # Create a second window for the remaining containers
    tmux new-window -t "$SESSION_NAME:1" -n "More" "$ALPINE_CMD"
    tmux split-window -h -t "$SESSION_NAME:1.0" "$NIX_CMD"

    # Set layout for both windows
    tmux select-layout -t "$SESSION_NAME:0" tiled
    tmux select-layout -t "$SESSION_NAME:1" even-horizontal

    # Start with the first window
    tmux select-window -t "$SESSION_NAME:0"

    tmux attach-session -t "$SESSION_NAME"

    success "Connected to all containers in tmux session"
}

attach_session() {
    if ! tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
        warn "Session '$SESSION_NAME' does not exist. Creating it..."
        create_session
        return
    fi

    slog "Attaching to session '$SESSION_NAME'..."
    tmux attach-session -t "$SESSION_NAME"
}

detach_session() {
    if [[ -z "${TMUX:-}" ]]; then
        fail "Not currently in a tmux session"
        exit 1
    fi

    slog "Detaching from tmux session..."
    tmux detach-client
}

destroy_session() {
    if ! tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
        warn "Session '$SESSION_NAME' does not exist"
        return
    fi

    slog "Destroying session '$SESSION_NAME'..."
    tmux kill-session -t "$SESSION_NAME"
    success "Session destroyed"
}

main() {
    check_distrobox
    check_tmux

    local command="${1:-}"

    case "$command" in
    create)
        create_session "true"
        ;;
    attach)
        attach_session
        ;;
    detach)
        detach_session
        ;;
    destroy)
        destroy_session
        ;;
    help | --help | -h)
        usage
        ;;
    "")
        create_session
        ;;
    *)
        fail "Unknown option: $command"
        usage
        exit 1
        ;;
    esac
}

main "$@"
