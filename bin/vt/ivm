#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

# shellcheck disable=SC1091
source "$DOT_DIR/share/fns"

usage() {
    cat <<EOF
Usage: $0 <command> [vm-name] [args...]

Manage Incus virtual machines similar to libvirt VM management.

COMMANDS:
    install                 Install Incus using ilmi
    list                    List all Incus VMs
    status <name>           Show VM status and info
    create <distro> [name]  Create a new Incus VM
    start <name>            Start a VM
    stop <name>             Stop a VM
    restart <name>          Restart a VM
    delete <name>           Delete a VM completely
    console <name>          Connect to VM console
    exec <name> <cmd>       Execute command in VM
    shell <n>            Get interactive shell in VM
    ip <name>               Get VM IP address
    ssh <name> [username]   Connect to VM via SSH
    info <name>             Show detailed VM information
    config <name>           Show VM configuration
    logs <n> [type]      Show VM logs (instance, console, or cloud-init-logs)
    snapshot <name> [snap]  Create VM snapshot
    restore <name> <snap>   Restore VM from snapshot
    copy <src> <dest>       Copy VM
    cleanup                 Remove stopped VMs
    usb-list                List all available USB devices on host
    usb-attached <n>        List USB devices attached to VM
    usb-attach <n> <device> [name]  Attach USB device to VM
    usb-detach <n> <device>         Detach USB device from VM
    disk-list               List all available block devices on host
    disk-attached <n>       List disk devices attached to VM
    disk-attach <n> <device> [name] Attach raw disk device to VM
    disk-detach <n> <device>        Detach disk device from VM

SUPPORTED DISTROS:
    ubuntu, fedora, arch, tumbleweed, debian, centos, alpine

EXAMPLES:
    $0 install                      # Install Incus
    $0 list                         # List all VMs
    $0 status ubuntu-vm             # Show status of 'ubuntu-vm'
    $0 create ubuntu myubuntu       # Create Ubuntu VM named 'myubuntu'
    $0 create fedora                # Create Fedora VM with default name
    $0 console ubuntu-vm            # Connect to VM console
    $0 exec ubuntu-vm "ls -la"      # Run command in VM
    $0 shell ubuntu-vm              # Get interactive shell in 'ubuntu-vm'
    $0 ip ubuntu-vm                 # Get IP address of 'ubuntu-vm'
    $0 ssh ubuntu-vm                # SSH to 'ubuntu-vm' (auto-detect username)
    $0 ssh ubuntu-vm ubuntu         # SSH to 'ubuntu-vm' as 'ubuntu' user
    $0 logs ubuntu-vm               # Show instance logs for 'ubuntu-vm'
    $0 logs ubuntu-vm console       # Show console logs for 'ubuntu-vm'
    $0 logs ubuntu-vm cloud-init-logs # Show cloud-init logs for 'ubuntu-vm'
    $0 snapshot ubuntu-vm backup    # Create snapshot named 'backup'
    $0 delete old-vm                # Delete 'old-vm' completely
    $0 usb-list                     # List available USB devices
    $0 usb-attach ubuntu-vm 1234:5678  # Attach USB device to VM
    $0 usb-detach ubuntu-vm my-usb      # Detach USB device from VM
    $0 disk-list                    # List available disk devices
    $0 disk-attach ubuntu-vm /dev/sdb   # Attach raw disk to VM
    $0 disk-detach ubuntu-vm /dev/sdb   # Detach disk from VM

EOF
}

check_incus() {
    if ! has_cmd incus; then
        fail "incus command not found. Please install Incus first."
        slog "You can install it with: $0 install"
        return 1
    fi
}

get_vm_state() {
    incus list "$1" --format csv --columns s | head -1
}

has_vm() {
    incus info "$1" >/dev/null 2>&1
}

check_vm() {
    if ! has_vm "$1"; then
        fail "VM '$1' not found"
        return 1
    fi
}

incus_list_vms() {
    local vm_name="${1:-}"
    local extra_args="${2:-}"

    if ! incus info >/dev/null 2>&1; then
        fail "Incus server is not reachable. Please initialize it first:"
        slog "  sudo incus admin init --minimal"
        slog "  newgrp incus"
        return 1
    fi

    if [[ -n "$vm_name" ]]; then
        incus list type=virtual-machine "$vm_name" "${extra_args}"
    else
        incus list type=virtual-machine "${extra_args}"
    fi
}

install_incus() {
    slog "Installing Incus using ilmi..."

    if has_cmd incus; then
        success "Incus already installed"
        return 0
    fi

    if has_cmd ilmi; then
        ilmi incus
    else
        fail "ilmi not found. Installing it first..."
        return 1
    fi

    if has_cmd incus; then
        success "Incus installed successfully!"
        slog "You may need to log out and back in for group changes to take effect."
        echo
        slog "You can now use:"
        slog "  $0 list                    # List VMs"
        slog "  $0 create ubuntu           # Create Ubuntu VM"
        slog "  ivm-create --distro ubuntu  # Create VM with cloud-init and SSH"
    else
        fail "Incus installation failed. Please check the output above for errors."
        return 1
    fi
}

list_vms() {
    check_incus || return 1

    slog "Listing all Incus VMs..."
    echo
    incus_list_vms
}

vm_status() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name"

    slog "Status for VM '$vm_name':"
    echo
    incus_list_vms "$vm_name"
    echo

    slog "Detailed information:"
    incus info "$vm_name" | head -20
}

create_vm() {
    local distro="$1"
    local vm_name="${2:-${distro}-vm}"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    slog "Creating $distro Incus VM: $vm_name"

    if declare -f "incus-${distro}-vm" >/dev/null 2>&1; then
        "incus-${distro}-vm" "$vm_name"
    else
        fail "Unsupported distro: $distro"
        slog "Supported distros: ubuntu, fedora, arch, tumbleweed, debian, centos, alpine"
        return 1
    fi

    if has_vm "$vm_name"; then
        success "VM '$vm_name' created successfully"
    else
        fail "Failed to create VM '$vm_name'"
        return 1
    fi
}

start_vm() {
    local vm_name="$1"
    check_incus || return 1

    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" == "RUNNING" ]]; then
        warn "VM '$vm_name' is already running"
        return 0
    fi

    slog "Starting VM '$vm_name'..."
    if incus start "$vm_name"; then
        success "VM '$vm_name' started"
    else
        fail "Failed to start VM '$vm_name'"
        return 1
    fi
}

stop_vm() {
    local vm_name="$1"
    check_incus || return 1

    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        warn "VM '$vm_name' is not running"
        return 0
    fi

    slog "Gracefully stopping VM '$vm_name'..."
    if incus stop "$vm_name"; then
        success "VM '$vm_name' stopped"
    else
        fail "Failed to stop VM '$vm_name'"
        return 1
    fi
}

restart_vm() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    slog "Restarting VM '$vm_name'..."
    if incus restart "$vm_name"; then
        success "VM '$vm_name' restarted"
    else
        fail "Failed to restart VM '$vm_name'"
        return 1
    fi
}

delete_vm() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    warn "This will permanently delete VM '$vm_name' and all its data!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Deletion cancelled"
        return 0
    fi

    local state
    state=$(get_vm_state "$vm_name")
    if [[ "$state" == "RUNNING" ]]; then
        slog "Stopping VM first..."
        incus stop "$vm_name" --force
    fi

    slog "Deleting VM '$vm_name'..."
    if incus delete "$vm_name"; then
        success "VM '$vm_name' deleted successfully"
    else
        fail "Failed to delete VM '$vm_name'"
        return 1
    fi
}

connect_console() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    slog "Connecting to console of VM '$vm_name'..."
    slog "Press Ctrl+a q to exit console"
    echo
    incus console "$vm_name"
}

exec_in_vm() {
    local vm_name="$1"
    shift
    local command="$*"
    check_incus || return 1

    if ! has_vm "$vm_name"; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    slog "Executing command in VM '$vm_name': $command"
    incus exec "$vm_name" -- "$@"
}

show_vm_info() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    slog "Detailed information for VM '$vm_name':"
    echo
    incus info "$vm_name"
}

show_vm_config() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    slog "Configuration for VM '$vm_name':"
    echo
    incus config show "$vm_name"
}

show_logs() {
    local vm_name="$1"
    local log_type="${2:-instance}"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    case "$log_type" in
    instance)
        slog "Showing instance logs for VM '$vm_name'..."
        echo
        incus info "$vm_name" --show-log
        ;;
    console)
        slog "Showing console logs for VM '$vm_name'..."
        echo
        incus console "$vm_name" --show-log
        ;;
    cloud-init-logs)
        if [[ "$state" != "RUNNING" ]]; then
            fail "VM '$vm_name' is not running"
            slog "Start it with: $0 start $vm_name"
            return 1
        fi

        slog "Showing cloud-init logs for VM '$vm_name'..."
        echo

        slog "=== Cloud-init main log (/var/log/cloud-init.log) ==="
        incus exec "$vm_name" -- tail -50 /var/log/cloud-init.log 2>/dev/null || {
            warn "Could not read /var/log/cloud-init.log"
        }

        echo
        slog "=== Cloud-init output log (/var/log/cloud-init-output.log) ==="
        incus exec "$vm_name" -- tail -50 /var/log/cloud-init-output.log 2>/dev/null || {
            warn "Could not read /var/log/cloud-init-output.log"
        }
        ;;
    *)
        fail "Invalid log type: $log_type"
        slog "Valid log types: instance, console, cloud-init-logs"
        return 1
        ;;
    esac
}

create_snapshot() {
    local vm_name="$1"
    local snapshot_name="${2:-snap-$(date +%Y%m%d-%H%M%S)}"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    slog "Creating snapshot '$snapshot_name' for VM '$vm_name'..."
    if incus snapshot "$vm_name" "$snapshot_name"; then
        success "Snapshot '$snapshot_name' created successfully"
    else
        fail "Failed to create snapshot '$snapshot_name'"
        return 1
    fi
}

restore_snapshot() {
    local vm_name="$1"
    local snapshot_name="$2"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    if [[ -z "$snapshot_name" ]]; then
        fail "Snapshot name required"
        return 1
    fi

    if ! incus info "$vm_name/$snapshot_name" >/dev/null 2>&1; then
        fail "Snapshot '$snapshot_name' not found"
        return 1
    fi

    warn "This will restore VM '$vm_name' to snapshot '$snapshot_name'"
    warn "All changes since the snapshot will be lost!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Restore cancelled"
        return 0
    fi

    slog "Restoring VM '$vm_name' from snapshot '$snapshot_name'..."
    if incus restore "$vm_name" "$snapshot_name"; then
        success "VM '$vm_name' restored from snapshot '$snapshot_name'"
    else
        fail "Failed to restore VM '$vm_name' from snapshot '$snapshot_name'"
        return 1
    fi
}

copy_vm() {
    local source_vm="$1"
    local dest_vm="$2"
    check_incus || return 1
    check_vm "$source_vm" || return 1
    check_vm "$dest_vm" && {
        fail "Destination VM '$dest_vm' already exists"
        return 1
    }

    if [[ -z "$dest_vm" ]]; then
        fail "Destination VM name required"
        return 1
    fi

    if ! incus info "$source_vm" >/dev/null 2>&1; then
        fail "Source VM '$source_vm' not found"
        return 1
    fi

    if incus info "$dest_vm" >/dev/null 2>&1; then
        fail "Destination VM '$dest_vm' already exists"
        return 1
    fi

    slog "Copying VM '$source_vm' to '$dest_vm'..."
    if incus copy "$source_vm" "$dest_vm"; then
        success "VM '$source_vm' copied to '$dest_vm'"
    else
        fail "Failed to copy VM '$source_vm' to '$dest_vm'"
        return 1
    fi
}

cleanup_vms() {
    check_incus || return 1

    slog "Cleaning up stopped VMs..."

    # List stopped VMs
    local stopped_vms
    stopped_vms=$(incus_list_vms "" "--format csv --columns n,s" | grep ",STOPPED$" | cut -d',' -f1)

    if [[ -n "$stopped_vms" ]]; then
        slog "Stopped VMs found:"
        echo "$stopped_vms"
        echo

        read -p "Remove all stopped VMs? (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            while IFS= read -r vm; do
                if [[ -n "$vm" ]]; then
                    slog "Removing stopped VM: $vm"
                    incus delete "$vm" 2>/dev/null || true
                fi
            done <<<"$stopped_vms"
        fi
    else
        slog "No stopped VMs found"
    fi

    success "Cleanup complete"
}

get_vm_ip() {
    local vm_name="$1"

    if ! incus info "$vm_name" >/dev/null 2>&1; then
        return 1
    fi

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        return 2
    fi

    # Try to get IP from incus list with network info
    local ip
    ip=$(incus list "^${vm_name}$" --format csv --columns 4 | head -1 | cut -d',' -f1 | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)

    # If that fails, try getting it from incus info
    if [[ -z "$ip" ]]; then
        ip=$(incus info "$vm_name" | grep -A 20 "Network usage:" | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1 | cut -d' ' -f2)
    fi

    # If still no IP, try alternative method
    if [[ -z "$ip" ]]; then
        ip=$(incus exec "$vm_name" -- ip -4 addr show | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | grep -v '127.0.0.1' | head -1 | cut -d' ' -f2 2>/dev/null)
    fi

    if [[ -z "$ip" ]]; then
        return 3
    fi

    echo "$ip"
    return 0
}

show_ip() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    local ip
    ip=$(get_vm_ip "$vm_name")
    local ret=$?

    case $ret in
    1)
        fail "VM '$vm_name' not found"
        return 1
        ;;
    2)
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for VM '$vm_name'"
        slog "VM may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        echo "$ip"
        return 0
        ;;
    esac
}

ssh_to_vm() {
    local vm_name="$1"
    local username="${2:-}"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    # Auto-detect username if not provided
    if [[ -z "$username" ]]; then
        username=$(detect_vm_user "$vm_name")
        slog "Auto-detected username: $username (override with: $0 ssh $vm_name <username>)"
    fi

    local ip
    ip=$(get_vm_ip "$vm_name")
    local ret=$?

    case $ret in
    1)
        fail "VM '$vm_name' not found"
        return 1
        ;;
    2)
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for VM '$vm_name'"
        slog "VM may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        slog "Connecting to $vm_name ($ip) as $username..."
        ssh "$username@$ip"
        ;;
    esac
}

detect_vm_user() {
    local vm_name="$1"

    # Extract base name from VM name (remove -vm suffix if present)
    local username="${vm_name%-vm}"

    # Handle special cases for common distros
    case "$username" in
    ubuntu*) username="ubuntu" ;;
    fedora*) username="fedora" ;;
    centos*) username="centos" ;;
    debian*) username="debian" ;;
    arch*) username="arch" ;;
    alpine*) username="alpine" ;;
    tumbleweed* | tw*) username="opensuse" ;;
    *)
        # Default to the VM name or common default
        if [[ "$username" =~ ^(ubuntu|fedora|centos|debian|arch|alpine|opensuse)$ ]]; then
            # Keep as is
            :
        else
            username="ubuntu" # Default fallback
        fi
        ;;
    esac

    echo "$username"
}

shell_to_vm() {
    local vm_name="$1"
    check_incus || return 1
    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    # Try to find an appropriate user for this VM
    local username
    username=$(detect_vm_user "$vm_name")

    # Check if the detected user exists in the VM
    if incus exec "$vm_name" -- id "$username" >/dev/null 2>&1; then
        slog "Entering shell of VM '$vm_name' as user '$username'..."
        incus exec "$vm_name" -- su - "$username"
    elif [[ "$username" != "ubuntu" ]] && incus exec "$vm_name" -- id "ubuntu" >/dev/null 2>&1; then
        # If the detected user doesn't exist, try ubuntu as fallback
        slog "User '$username' not found, entering shell of VM '$vm_name' as user 'ubuntu'..."
        incus exec "$vm_name" -- su - "ubuntu"
    else
        # Fall back to root if no suitable user found
        slog "No suitable user found, entering shell of VM '$vm_name' as root..."
        incus shell "$vm_name"
    fi
}

# Main command handling
# USB device management functions
list_host_usb_devices() {
    slog "Available USB devices on host:"
    echo
    printf "%-3s %-6s %-9s %s\n" "Bus" "Device" "ID" "Description"
    printf "%-3s %-6s %-9s %s\n" "---" "------" "---------" "-----------"

    while IFS= read -r line; do
        if [[ "$line" =~ ^Bus\ ([0-9]+)\ Device\ ([0-9]+):\ ID\ ([0-9a-f]{4}):([0-9a-f]{4})\ (.*)$ ]]; then
            local bus="${BASH_REMATCH[1]}"
            local device="${BASH_REMATCH[2]}"
            local vendor="${BASH_REMATCH[3]}"
            local product="${BASH_REMATCH[4]}"
            local description="${BASH_REMATCH[5]}"
            printf "%-3s %-6s %s:%s %s\n" "$bus" "$device" "$vendor" "$product" "$description"
        fi
    done < <(lsusb)
    echo
    slog "Usage examples:"
    slog "  By USB ID: $0 usb-attach VM_NAME vendor:product"
    slog "  By Bus/Device: $0 usb-attach VM_NAME bus.device"
}

list_vm_usb_devices() {
    local vm_name="$1"
    check_vm "$vm_name" || return 1

    slog "USB devices attached to VM '$vm_name':"
    echo
    incus config device list "$vm_name" | grep -E "^usb" || {
        slog "No USB devices attached to VM '$vm_name'"
    }
}

attach_usb_device() {
    local vm_name="$1"
    local device_spec="$2"
    local device_name="${3:-usb-$(date +%s)}"

    check_vm "$vm_name" || return 1

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' must be running to attach USB devices"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    # Parse device specification
    local vendor_id product_id bus device
    if [[ "$device_spec" =~ ^([0-9a-f]{4}):([0-9a-f]{4})$ ]]; then
        vendor_id="${BASH_REMATCH[1]}"
        product_id="${BASH_REMATCH[2]}"
        slog "Attaching USB device $vendor_id:$product_id to VM '$vm_name' as '$device_name'..."
        incus config device add "$vm_name" "$device_name" usb vendorid="$vendor_id" productid="$product_id"
    elif [[ "$device_spec" =~ ^([0-9]+)\.([0-9]+)$ ]]; then
        bus="${BASH_REMATCH[1]}"
        device="${BASH_REMATCH[2]}"
        slog "Attaching USB device bus $bus device $device to VM '$vm_name' as '$device_name'..."
        incus config device add "$vm_name" "$device_name" usb busnum="$bus" devnum="$device"
    else
        fail "Invalid device specification: $device_spec"
        slog "Use format: vendor:product (e.g., 1234:5678) or bus.device (e.g., 1.2)"
        return 1
    fi

    if [[ $? -eq 0 ]]; then
        success "USB device attached successfully as '$device_name'"
    else
        fail "Failed to attach USB device"
        return 1
    fi
}

detach_usb_device() {
    local vm_name="$1"
    local device_name="$2"

    check_vm "$vm_name" || return 1

    slog "Detaching USB device '$device_name' from VM '$vm_name'..."
    if incus config device remove "$vm_name" "$device_name"; then
        success "USB device '$device_name' detached successfully"
    else
        fail "Failed to detach USB device '$device_name'"
        return 1
    fi
}

# Disk device management functions
list_host_disk_devices() {
    slog "Available block devices on host:"
    echo
    lsblk -o NAME,SIZE,TYPE,MOUNTPOINT,MODEL | grep -E "(disk|part)" || {
        warn "No block devices found"
    }
    echo
    slog "Usage examples:"
    slog "  $0 disk-attach VM_NAME /dev/sdb"
    slog "  $0 disk-attach VM_NAME /dev/sdb1 my-disk"
}

list_vm_disk_devices() {
    local vm_name="$1"
    check_vm "$vm_name" || return 1

    slog "Disk devices attached to VM '$vm_name':"
    echo
    incus config device list "$vm_name" | grep -E "^disk" || {
        slog "No additional disk devices attached to VM '$vm_name'"
    }
}

attach_disk_device() {
    local vm_name="$1"
    local device_path="$2"
    local device_name="${3:-disk-$(basename "$device_path")}"

    check_vm "$vm_name" || return 1

    if [[ ! -b "$device_path" ]]; then
        fail "Device '$device_path' is not a valid block device"
        return 1
    fi

    local state
    state=$(get_vm_state "$vm_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "VM '$vm_name' must be running to attach disk devices"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    slog "Attaching disk device '$device_path' to VM '$vm_name' as '$device_name'..."
    if incus config device add "$vm_name" "$device_name" disk source="$device_path"; then
        success "Disk device attached successfully as '$device_name'"
    else
        fail "Failed to attach disk device"
        return 1
    fi
}

detach_disk_device() {
    local vm_name="$1"
    local device_name="$2"

    check_vm "$vm_name" || return 1

    slog "Detaching disk device '$device_name' from VM '$vm_name'..."
    if incus config device remove "$vm_name" "$device_name"; then
        success "Disk device '$device_name' detached successfully"
    else
        fail "Failed to detach disk device '$device_name'"
        return 1
    fi
}

if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
vm_name="${2:-}"

case "$command" in
install)
    install_incus
    ;;
list)
    list_vms
    ;;
status)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    vm_status "$vm_name"
    ;;
create)
    [[ -z "$vm_name" ]] && {
        fail "Distro name required"
        usage
        exit 1
    }
    create_vm "$vm_name" "${3:-}"
    ;;
start)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    start_vm "$vm_name"
    ;;
stop)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    stop_vm "$vm_name"
    ;;
restart)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    restart_vm "$vm_name"
    ;;
delete)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    delete_vm "$vm_name"
    ;;
console)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    connect_console "$vm_name"
    ;;
exec)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    [[ $# -lt 3 ]] && {
        fail "Command required"
        usage
        exit 1
    }
    exec_in_vm "$vm_name" "${@:3}"
    ;;
shell)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    shell_to_vm "$vm_name"
    ;;
ip)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_ip "$vm_name"
    ;;
ssh)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    ssh_to_vm "$vm_name" "${3:-}"
    ;;
info)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_vm_info "$vm_name"
    ;;
config)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_vm_config "$vm_name"
    ;;
logs)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_logs "$vm_name" "${3:-}"
    ;;
snapshot)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    create_snapshot "$vm_name" "${3:-}"
    ;;
restore)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    [[ -z "${3:-}" ]] && {
        fail "Snapshot name required"
        usage
        exit 1
    }
    restore_snapshot "$vm_name" "$3"
    ;;
copy)
    [[ -z "$vm_name" ]] && {
        fail "Source VM name required"
        usage
        exit 1
    }
    [[ -z "${3:-}" ]] && {
        fail "Destination VM name required"
        usage
        exit 1
    }
    copy_vm "$vm_name" "$3"
    ;;
cleanup)
    cleanup_vms
    ;;
usb-list)
    list_host_usb_devices
    ;;
usb-attached)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    list_vm_usb_devices "$vm_name"
    ;;
usb-attach)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    device_spec="${3:-}"
    [[ -z "$device_spec" ]] && {
        fail "Device specification required"
        usage
        exit 1
    }
    attach_usb_device "$vm_name" "$device_spec" "${4:-}"
    ;;
usb-detach)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    device_name="${3:-}"
    [[ -z "$device_name" ]] && {
        fail "Device name required"
        usage
        exit 1
    }
    detach_usb_device "$vm_name" "$device_name"
    ;;
disk-list)
    list_host_disk_devices
    ;;
disk-attached)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    list_vm_disk_devices "$vm_name"
    ;;
disk-attach)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    device_path="${3:-}"
    [[ -z "$device_path" ]] && {
        fail "Device path required"
        usage
        exit 1
    }
    attach_disk_device "$vm_name" "$device_path" "${4:-}"
    ;;
disk-detach)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    device_name="${3:-}"
    [[ -z "$device_name" ]] && {
        fail "Device name required"
        usage
        exit 1
    }
    detach_disk_device "$vm_name" "$device_name"
    ;;
--help | -h)
    usage
    ;;
*)
    fail "Unknown command: $command"
    usage
    exit 1
    ;;
esac
