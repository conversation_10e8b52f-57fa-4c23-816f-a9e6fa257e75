#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {
    has_cmd distrobox || {
        fail "distrobox is not installed. Please install it first."
        slog "You can install it with: dt install"
        exit 1
    }

    slog "Creating 6 distrobox containers (Ubuntu, Fedora, Arch, Debian, Alpine, NixOS)..."

    slog "Creating Ubuntu container: ubuntu"
    dt create ubuntu ubuntu

    slog "Creating Fedora container: fedora"
    dt create fedora fedora

    slog "Creating Arch container: arch"
    dt create arch arch

    slog "Creating Debian container: debian"
    dt create debian debian

    slog "Creating Alpine container: alpine"
    dt create alpine alpine

    slog "Creating NixOS container: nix"
    dt create nix nix

    slog "Listing created containers:"
    dt list

    success "All containers created successfully!"
    slog "You can access them using: dt enter <container-name>"
}

main "$@"
