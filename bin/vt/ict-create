#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

# Default values
unset CONTAINER_NAME
unset <PERSON>IS<PERSON><PERSON>
unset <PERSON><PERSON><PERSON>SE
unset US<PERSON><PERSON><PERSON>
unset PA<PERSON><PERSON>ORD
unset SSH_KEY
unset IMAGE

VCPUS="2"
MEMORY_MB="1024"
PRIVILEGED="false"

usage() {
    cat <<EOF
Usage: $0 --distro DISTRO [OPTIONS]

Create Incus LXC containers with cloud-init and SSH access.

REQUIRED:
    --distro DISTRO         Distribution (ubuntu, fedora, arch, debian, centos, alpine, tumbleweed)

OPTIONS:
    --name NAME             Container name (default: distro name)
    --release RELEASE       Distribution release (default: latest)
    --username USER         Username for container (default: distro default)
    --password PASS         User password (default: container name)
    --vcpus NUM             Number of vCPUs (default: 2)
    --memory MB             RAM in MB (default: 1024)
    --ssh-key PATH          SSH public key path (default: auto-detect)
    --privileged            Create privileged container (default: false)
    --help, -h              Show this help

EXAMPLES:
    $0 --distro ubuntu
    $0 --distro fedora --name my-fedora --vcpus 4 --memory 2048
    $0 --distro debian --username admin --password mypass
    $0 --distro arch --release current --privileged
    $0 --distro tumbleweed --name opensuse-container --vcpus 2 --memory 2048

SUPPORTED DISTRIBUTIONS:
    ubuntu      - Ubuntu LTS (24.04) or specified release
    fedora      - Fedora (latest) or specified release
    arch        - Arch Linux (current)
    debian      - Debian (12/bookworm) or specified release
    centos      - CentOS Stream (9) or specified release
    alpine      - Alpine Linux (3.19) or specified release
    tumbleweed  - openSUSE Tumbleweed (rolling release)

NOTE: LXC containers are lightweight and share the host kernel.
      They start faster and use fewer resources than VMs.

EOF
}

init_ssh() {
    if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
        SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
    elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
        SSH_KEY="${HOME}/.ssh/id_rsa.pub"
    else
        warn "No SSH key found. Generating new RSA key pair..."
        ssh-keygen -t rsa -b 4096 -f "${HOME}/.ssh/id_rsa" -N ""
        SSH_KEY="${HOME}/.ssh/id_rsa.pub"
    fi
}

check_prerequisites() {
    if ! has_cmd incus; then
        fail "incus command not found. Please install Incus first."
        exit 1
    fi

    if ! incus info >/dev/null 2>&1; then
        fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
        exit 1
    fi

    init_ssh

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        exit 1
    fi

    slog "Using SSH key: $SSH_KEY"
}

configure_distribution() {
    case "$DISTRO" in
    ubuntu)
        RELEASE=${RELEASE:-"24.04"}
        USERNAME=${USERNAME:-"ubuntu"}
        IMAGE="images:ubuntu/${RELEASE}/cloud"
        ;;
    fedora)
        RELEASE=${RELEASE:-"42"}
        USERNAME=${USERNAME:-"fedora"}
        IMAGE="images:fedora/${RELEASE}/cloud"
        ;;
    arch)
        RELEASE=${RELEASE:-"current"}
        USERNAME=${USERNAME:-"arch"}
        IMAGE="images:archlinux/${RELEASE}/cloud"
        ;;
    debian)
        RELEASE=${RELEASE:-"12"}
        USERNAME=${USERNAME:-"debian"}
        IMAGE="images:debian/${RELEASE}/cloud"
        ;;
    centos)
        RELEASE=${RELEASE:-"9-Stream"}
        USERNAME=${USERNAME:-"centos"}
        IMAGE="images:centos/${RELEASE}/cloud"
        ;;
    alpine)
        RELEASE=${RELEASE:-"3.19"}
        USERNAME=${USERNAME:-"alpine"}
        IMAGE="images:alpine/${RELEASE}/cloud"
        ;;
    tumbleweed)
        RELEASE=${RELEASE:-"current"}
        USERNAME=${USERNAME:-"opensuse"}
        IMAGE="images:opensuse/tumbleweed/cloud"
        ;;
    *)
        fail "Unsupported distribution: $DISTRO"
        fail "Supported distributions: ubuntu, fedora, arch, debian, centos, alpine, tumbleweed"
        exit 1
        ;;
    esac

    CONTAINER_NAME=${CONTAINER_NAME:-"$DISTRO"}
    PASSWORD=${PASSWORD:-"$USERNAME"}

    slog "Configuration:"
    slog "  Distribution: $DISTRO $RELEASE"
    slog "  Container Name: $CONTAINER_NAME"
    slog "  Username: $USERNAME"
    slog "  Image: $IMAGE"
    slog "  Resources: ${VCPUS} vCPUs, ${MEMORY_MB}MB RAM"
    slog "  Privileged: $PRIVILEGED"
}

generate_cloud_init_config() {
    slog "Generating cloud-init configuration..."

    # Create temporary directory for cloud-init files
    CLOUD_INIT_DIR=$(mktemp -d)

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    local packages_common="curl wget vim htop git unzip"
    local openssh_pkg

    if [[ "$DISTRO" == "arch" ]]; then
        openssh_pkg="openssh"
    else
        openssh_pkg="openssh-server"
    fi

    # Create cloud-init user-data file using tee
    tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $CONTAINER_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups:
      - sudo
      - wheel
      - adm
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: $(openssl passwd -6 "$PASSWORD")
    ssh_authorized_keys:
      - "$pub_key"

# System configuration
package_update: true
package_upgrade: true

packages:
  - $openssh_pkg
$(for pkg in $packages_common; do echo "  - $pkg"; done)

# Enable SSH service and ensure proper configuration
runcmd:
  - systemctl enable --now ssh || systemctl enable --now sshd
  - systemctl enable --now systemd-resolved || true

# Network configuration
write_files:
  - path: /etc/motd
    content: |
      Welcome to $CONTAINER_NAME ($DISTRO $RELEASE)!
      Created with ict-create

      SSH access configured for user: $USERNAME
      Type: LXC Container (lightweight, shared kernel)

final_message: "Container $CONTAINER_NAME setup complete! SSH access is ready."
EOF

    # Create meta-data file
    tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${CONTAINER_NAME}-$(date +%s)
local-hostname: $CONTAINER_NAME
EOF

    success "Cloud-init configuration files created in: $CLOUD_INIT_DIR"
}

create_container() {
    slog "Creating Incus LXC container '$CONTAINER_NAME'..."

    # Check if container already exists
    if incus info "$CONTAINER_NAME" >/dev/null 2>&1; then
        fail "Container '$CONTAINER_NAME' already exists"
        exit 1
    fi

    # Generate cloud-init configuration files
    generate_cloud_init_config

    # Create the container with cloud-init configuration
    slog "Launching container with image: $IMAGE"

    local launch_args=(
        "$IMAGE" "$CONTAINER_NAME"
        --config "limits.cpu=$VCPUS"
        --config "limits.memory=${MEMORY_MB}MB"
        --config "user.user-data=$(cat "${CLOUD_INIT_DIR}/user-data")"
        --config "user.meta-data=$(cat "${CLOUD_INIT_DIR}/meta-data")"
    )

    # Add privileged flag if requested
    if [[ "$PRIVILEGED" == "true" ]]; then
        launch_args+=(--config "security.privileged=true")
        slog "Creating privileged container"
    fi

    if ! incus launch "${launch_args[@]}"; then
        fail "Failed to create container '$CONTAINER_NAME'"
        exit 1
    fi

    success "Container '$CONTAINER_NAME' created successfully"

    # Clean up temporary cloud-init directory
    if [[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]]; then
        rm -rf "$CLOUD_INIT_DIR"
        slog "Cleaned up temporary cloud-init files"
    fi
}

get_container_ip() {
    slog "Getting container IP address..."

    local ip
    local max_attempts=30
    local attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        ip=$(incus list "$CONTAINER_NAME" --format csv --columns 4 | head -1 | cut -d' ' -f1)

        if [[ -n "$ip" && "$ip" != "-" ]]; then
            echo "$ip"
            return 0
        fi

        sleep 2
        ((attempt++))
    done

    warn "Could not determine container IP address"
    return 1
}

show_completion_info() {
    success "Container '$CONTAINER_NAME' is ready!"
    echo
    slog "Container Details:"
    incus list "$CONTAINER_NAME"
    echo

    local ip
    if ip=$(get_container_ip); then
        slog "SSH Access:"
        slog "  IP Address: $ip"
        slog "  Username: $USERNAME"
        slog "  SSH Command: ssh $USERNAME@$ip"
        echo
        slog "You can also use Incus commands:"
        slog "  Shell: incus exec $CONTAINER_NAME -- /bin/bash"
        slog "  Execute: incus exec $CONTAINER_NAME -- <command>"
    else
        slog "Use Incus commands to access the container:"
        slog "  Shell: incus exec $CONTAINER_NAME -- /bin/bash"
        slog "  Execute: incus exec $CONTAINER_NAME -- <command>"
    fi

    echo
    slog "Container Management:"
    slog "  Status: ict status $CONTAINER_NAME"
    slog "  Stop: ict stop $CONTAINER_NAME"
    slog "  Start: ict start $CONTAINER_NAME"
    slog "  Delete: ict delete $CONTAINER_NAME"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        --distro)
            DISTRO="$2"
            shift 2
            ;;
        --name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --release)
            RELEASE="$2"
            shift 2
            ;;
        --username)
            USERNAME="$2"
            shift 2
            ;;
        --password)
            PASSWORD="$2"
            shift 2
            ;;
        --vcpus)
            VCPUS="$2"
            shift 2
            ;;
        --memory)
            MEMORY_MB="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --privileged)
            PRIVILEGED="true"
            shift
            ;;
        --help | -h)
            usage
            exit 0
            ;;
        *)
            fail "Unknown option: $1"
            usage
            exit 1
            ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$DISTRO" ]]; then
        fail "Distribution is required. Use --distro option."
        usage
        exit 1
    fi
}

main() {
    slog "Starting Incus LXC container creation..."

    parse_args "$@"
    check_prerequisites
    configure_distribution
    create_container
    show_completion_info

    success "All done! Your $DISTRO container '$CONTAINER_NAME' is ready to use."
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
