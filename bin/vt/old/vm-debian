#!/bin/bash

set -euo pipefail

# Initial variable setup
VM_NAME="debian"

VCPUS=4
RAM_MB=4096
DISK_SIZE="40G"
WORKDIR="/var/lib/libvirt/images/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
BRIDGE_IF="virbr0"

BASE_IMG="${WORKDIR}/debian-${DEBIAN_RELEASE}.qcow2"
DEBIAN_RELEASE="bookworm" # 12
USERNAME="debian"
DEFAULT_PASSWORD="$VM_NAME"

if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
  SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
  SSH_KEY="${HOME}/.ssh/id_rsa.pub"
else
  ssh-keygen -t rsa -b 4096 -f "${HOME}/.ssh/id_rsa" -N ""
  SSH_KEY="${HOME}/.ssh/id_rsa.pub"
fi

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

PASSWORD_HASH=$(openssl passwd -6 "$DEFAULT_PASSWORD")
log_info "Generated password hash for user '$USERNAME'"

cleanup_on_error() {
  log_warn "Cleaning up due to error..."
  sudo rm -f "$DISK_IMG" 2>/dev/null || true
  sudo rm -f "$SEED_ISO" 2>/dev/null || true
  sudo virsh destroy "$VM_NAME" 2>/dev/null || true
  sudo virsh undefine "$VM_NAME" 2>/dev/null || true

  stop_sudo_keepalive
}

trap cleanup_on_error ERR

usage() {
  cat <<EOF
Usage: $0 [OPTIONS]
  --name NAME         VM name (default: $VM_NAME)
  --memory MB         VM memory in MB (default: $RAM_MB)
  --vcpus VCPUS       Number of vCPUs (default: $VCPUS)
  --disk-size SIZE    Disk size (default: $DISK_SIZE)
  --ssh-key PATH      SSH public key path (default: $SSH_KEY)
  --bridge BRIDGE     Network bridge (default: $BRIDGE_IF)
  --debian-release REL Debian release (default: $DEBIAN_RELEASE)
  --username USER     VM username (default: $USERNAME)
  --help, -h          Show this help

EOF
}

while [[ $# -gt 0 ]]; do
  case $1 in
  --name)
    VM_NAME="$2"
    WORKDIR="/var/lib/libvirt/images/${VM_NAME}-vm"
    CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
    BASE_IMG="${WORKDIR}/debian-${DEBIAN_RELEASE}.qcow2"
    DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
    SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
    shift 2
    ;;
  --memory)
    RAM_MB="$2"
    shift 2
    ;;
  --vcpus)
    VCPUS="$2"
    shift 2
    ;;
  --disk-size)
    DISK_SIZE="$2"
    shift 2
    ;;
  --ssh-key)
    SSH_KEY="$2"
    shift 2
    ;;
  --bridge)
    BRIDGE_IF="$2"
    shift 2
    ;;
  --debian-release)
    DEBIAN_RELEASE="$2"
    BASE_IMG="${WORKDIR}/debian-${DEBIAN_RELEASE}.qcow2"
    shift 2
    ;;
  --username)
    USERNAME="$2"
    shift 2
    ;;
  --help | -h)
    usage
    exit 0
    ;;
  *)
    log_error "Unknown option: $1"
    usage
    exit 1
    ;;
  esac
done

check_prerequisites() {
  log_info "Checking prerequisites..."

  # Check for sudo privileges
  if ! sudo -v; then
    log_error "This script requires sudo privileges to create files in system directories"
    exit 1
  fi

  # Determine QEMU user and group
  if getent passwd libvirt-qemu >/dev/null; then
    QEMU_USER="libvirt-qemu"
    QEMU_GROUP="libvirt-qemu"
  elif getent passwd qemu >/dev/null; then
    QEMU_USER="qemu"
    QEMU_GROUP="qemu"
  elif getent passwd libvirt >/dev/null; then
    QEMU_USER="libvirt"
    QEMU_GROUP="libvirt"
  else
    log_warn "Could not determine QEMU user, using 'root:kvm' as fallback"
    QEMU_USER="root"
    QEMU_GROUP="kvm"
  fi

  log_info "Using QEMU user/group: $QEMU_USER:$QEMU_GROUP"

  if ! groups | grep -q libvirt; then
    log_warn "User not in libvirt group. You may need sudo for virsh commands"
    log_warn "Add user to group: sudo usermod -a -G libvirt \$USER"
  fi

  if [[ ! -f "$SSH_KEY" ]]; then
    log_error "SSH public key not found at: $SSH_KEY"
    log_error "Generate with: ssh-keygen -t rsa -C '<EMAIL>'"
    exit 1
  fi

  if virsh list --all | grep -q "$VM_NAME"; then
    log_error "VM '$VM_NAME' already exists"
    log_error "Remove with: virsh destroy $VM_NAME && virsh undefine $VM_NAME"
    exit 1
  fi

  if ! ip link show "$BRIDGE_IF" &>/dev/null; then
    log_warn "Bridge '$BRIDGE_IF' not found, will use default libvirt network"
    BRIDGE_IF="default"
  fi

  log_success "Prerequisites check passed"
}

start_sudo_keepalive() {
  log_info "Starting sudo keepalive process..."
  sudo -v
  (while true; do
    sudo -v
    sleep 50
  done) &
  SUDO_KEEPALIVE_PID=$!
}

stop_sudo_keepalive() {
  if [ -n "$SUDO_KEEPALIVE_PID" ] && kill -0 "$SUDO_KEEPALIVE_PID" 2>/dev/null; then
    log_info "Stopping sudo keepalive process..."
    kill "$SUDO_KEEPALIVE_PID" 2>/dev/null
  fi
  SUDO_KEEPALIVE_PID=""
}

download_debian_image() {
  log_info "Preparing Debian cloud image..."

  if [[ -f "$BASE_IMG" ]]; then
    log_info "Using existing Debian image: $BASE_IMG"
    return 0
  fi

  log_info "Downloading Debian $DEBIAN_RELEASE cloud image..."

  # Updated URL format for Debian cloud images
  local download_url="https://cloud.debian.org/images/cloud/bookworm/latest/debian-12-generic-amd64.qcow2"

  # Handle different Debian releases
  if [[ "$DEBIAN_RELEASE" == "bookworm" ]]; then
    download_url="https://cloud.debian.org/images/cloud/bookworm/latest/debian-12-generic-amd64.qcow2"
  elif [[ "$DEBIAN_RELEASE" == "bullseye" ]]; then
    download_url="https://cloud.debian.org/images/cloud/bullseye/latest/debian-11-generic-amd64.qcow2"
  elif [[ "$DEBIAN_RELEASE" == "trixie" ]]; then
    download_url="https://cloud.debian.org/images/cloud/trixie/latest/debian-13-generic-amd64.qcow2"
  else
    log_error "Unsupported Debian release: $DEBIAN_RELEASE"
    exit 1
  fi

  log_info "URL: $download_url"

  sudo mkdir -p "$WORKDIR"
  sudo chown "$QEMU_USER:$QEMU_GROUP" "$WORKDIR"
  sudo chmod 755 "$WORKDIR"

  if ! sudo wget -O "$BASE_IMG" "$download_url"; then
    log_error "Failed to download Debian cloud image"
    sudo rm -f "$BASE_IMG"
    exit 1
  fi

  sudo chown "$QEMU_USER:$QEMU_GROUP" "$BASE_IMG"
  sudo chmod 644 "$BASE_IMG"
  log_success "Debian cloud image downloaded: $BASE_IMG"
}

create_vm_disk() {
  log_info "Creating VM disk..."

  if ! sudo cp "$BASE_IMG" "$DISK_IMG"; then
    log_error "Failed to copy base image"
    exit 1
  fi

  if ! sudo qemu-img resize "$DISK_IMG" "$DISK_SIZE"; then
    log_error "Failed to resize VM disk"
    exit 1
  fi

  sudo chown "$QEMU_USER:$QEMU_GROUP" "$DISK_IMG"
  sudo chmod 644 "$DISK_IMG"
  log_success "VM disk created: $DISK_IMG ($DISK_SIZE)"
}

generate_cloud_init() {
  log_info "Generating cloud-init configuration..."

  sudo mkdir -p "$CLOUD_INIT_DIR"
  sudo chown "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
  sudo chmod 755 "$CLOUD_INIT_DIR"

  local pub_key
  pub_key=$(cat "$SSH_KEY")

  sudo tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - $pub_key

# System configuration
package_update: true
package_upgrade: true

packages:
  - qemu-guest-agent
  - curl
  - wget
  - vim
  - htop
  - git
  - unzip
  - ca-certificates
  - gnupg
  - lsb-release

runcmd:
  - systemctl enable --now qemu-guest-agent
  - touch /home/<USER>/vm-setup-complete
  - chown $USERNAME:$USERNAME /home/<USER>/vm-setup-complete
  - chown -R $USERNAME:$USERNAME /home/<USER>

timezone: UTC

write_files:
  - path: /etc/motd
    content: |
      Welcome to $VM_NAME VM!

      User: $USERNAME (passwordless sudo enabled)

    append: false

final_message: "VM $VM_NAME setup complete!"
EOF

  sudo tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

  sudo tee "${CLOUD_INIT_DIR}/network-config" >/dev/null <<EOF
version: 2
ethernets:
  enp1s0:
    dhcp4: true
EOF

  sudo chown -R "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
  sudo chmod -R 644 "$CLOUD_INIT_DIR"/*
  sudo chmod 755 "$CLOUD_INIT_DIR"
  log_success "Cloud-init configuration generated"
}

create_cloud_init_iso() {
  log_info "Creating cloud-init ISO..."

  if ! sudo mkisofs -output "$SEED_ISO" -volid cidata -joliet -rock \
    "${CLOUD_INIT_DIR}/user-data" \
    "${CLOUD_INIT_DIR}/meta-data" \
    "${CLOUD_INIT_DIR}/network-config" 2>/dev/null; then
    log_error "Failed to create cloud-init ISO"
    exit 1
  fi

  sudo chown "$QEMU_USER:$QEMU_GROUP" "$SEED_ISO"
  sudo chmod 644 "$SEED_ISO"
  log_success "Cloud-init ISO created: $SEED_ISO"
}

launch_vm() {
  log_info "Launching VM '$VM_NAME'..."

  local network_config
  if [[ "$BRIDGE_IF" == "default" ]]; then
    network_config="network=default,model=virtio"
  else
    network_config="bridge=$BRIDGE_IF,model=virtio"
  fi

  # Determine OS variant
  local os_variant="debian12"
  if [[ "$DEBIAN_RELEASE" == "bullseye" ]]; then
    os_variant="debian11"
  elif [[ "$DEBIAN_RELEASE" == "bookworm" ]]; then
    os_variant="debian12"
  elif [[ "$DEBIAN_RELEASE" == "trixie" ]]; then
    os_variant="debian12" # Use debian12 for now until trixie is recognized
  fi

  if
    ! virt-install \
      --connect qemu:///system \
      --name "$VM_NAME" \
      --memory "$RAM_MB" \
      --vcpus "$VCPUS" \
      --disk path="$DISK_IMG",format=qcow2,bus=virtio \
      --disk path="$SEED_ISO",device=cdrom \
      --os-variant "$os_variant" \
      --virt-type kvm \
      --graphics none \
      --network "$network_config" \
      --import \
      --noautoconsole
  then
    log_error "Failed to create VM"
    exit 1
  fi

  log_success "VM '$VM_NAME' created successfully!"

  # Add VM to /etc/hosts for easy access
  log_info "Adding VM to /etc/hosts for name-based access..."
  sleep 5 # Give VM time to get IP
  if [[ -x "$HOME/.ilm/bin/vim-hosts" ]]; then
    "$HOME"/.ilm/bin/vim-hosts add "$VM_NAME" || log_warn "Could not add VM to /etc/hosts automatically"
  fi
}

show_completion_info() {
  log_info "VM creation complete!"
  log_info "VM Name: $VM_NAME"
  log_info "Working Directory: $WORKDIR"
  log_info "Cloud-init Directory: $CLOUD_INIT_DIR"
  log_info "Base Image: $BASE_IMG"
  log_info "VM Disk: $DISK_IMG"
  log_info "Cloud-init ISO: $SEED_ISO"
  log_info "Bridge Interface: $BRIDGE_IF"
  log_info "Username: $USERNAME"
  log_info "SSH Key: $SSH_KEY"
  log_info "Debian Release: $DEBIAN_RELEASE"
  log_info "Disk Size: $DISK_SIZE"
  log_info "RAM: $RAM_MB MB"
  log_info "vCPUs: $VCPUS"

  echo
  echo "  Connect to console: virsh console $VM_NAME"
  echo "  Find VM IP:         virsh domifaddr $VM_NAME"
  echo "  SSH by name:        ssh $USERNAME@$VM_NAME"
  echo "  SSH by IP:          ssh $USERNAME@<VM_IP>"
  echo
  log_warn "Note: Cloud-init setup takes 2-3 minutes. Check /home/<USER>/vm-setup-complete for completion."
}

main() {
  log_info "Starting Debian VM creation..."
  log_info "VM Name: $VM_NAME"
  log_info "Working Directory: $WORKDIR"

  check_prerequisites
  start_sudo_keepalive
  download_debian_image
  create_vm_disk
  generate_cloud_init
  create_cloud_init_iso
  launch_vm
  show_completion_info

  log_success "All done! Your Debian VM is ready."
}

main "$@"
