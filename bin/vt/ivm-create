#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

# Default values
unset VM_NAME
unset DISTRO
unset R<PERSON><PERSON>SE
unset US<PERSON><PERSON><PERSON>
unset <PERSON><PERSON><PERSON>ORD
unset SSH_KEY
unset IMAGE

VCPUS="2"
MEMORY_MB="2048"
DISK_SIZE="20GB"
BRIDGE_IF="incusbr0"
DOTFILES_OPTIONS=""
INSTALL_NIX=false

usage() {
    cat <<EOF
Usage: $0 --distro DISTRO [OPTIONS]

Create Incus virtual machines with cloud-init and SSH access.

REQUIRED:
    --distro DISTRO         Distribution (ubuntu, fedora, arch, debian, centos, alpine, tumbleweed)

OPTIONS:
    --name NAME             VM name (default: distro name)
    --release RELEASE       Distribution release (default: latest)
    --username USER         Username for VM (default: distro default)
    --password PASS         User password (default: vm name)
    --vcpus NUM             Number of vCPUs (default: 2)
    --memory MB             RAM in MB (default: 2048)
    --disk-size SIZE        Disk size (default: 20GB)
    --ssh-key PATH          SSH public key path (default: auto-detect)
    --bridge BRIDGE         Network bridge (default: incusbr0)
    --nix                   Install Nix using Determinate Systems installer
    --dotfiles OPTIONS...   Install dotfiles with specified options (must be last)
    --help, -h              Show this help

EXAMPLES:
    $0 --distro ubuntu
    $0 --distro fedora --name my-fedora --vcpus 4 --memory 4096
    $0 --distro debian --username admin --password mypass
    $0 --distro arch --release current --disk-size 40GB
    $0 --distro tumbleweed --name opensuse-vm --vcpus 2 --memory 4096
    $0 --distro ubuntu --nix            # Ubuntu VM with Nix package manager
    $0 --distro ubuntu --name ubuntu-dev --vcpus 4 --memory 8192 --dotfiles slim-shell docker code-server

SUPPORTED DISTRIBUTIONS:
    ubuntu      - Ubuntu LTS (24.04) or specified release
    fedora      - Fedora (latest) or specified release
    arch        - Arch Linux (current)
    debian      - Debian (12/bookworm) or specified release
    centos      - CentOS Stream (9) or specified release
    alpine      - Alpine Linux (3.19) or specified release
    tumbleweed  - openSUSE Tumbleweed (rolling release)

EOF
}

init_ssh() {
    if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
        SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
    elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
        SSH_KEY="${HOME}/.ssh/id_rsa.pub"
    else
        warn "No SSH key found. Generating new RSA key pair..."
        ssh-keygen -t rsa -b 4096 -f "${HOME}/.ssh/id_rsa" -N ""
        SSH_KEY="${HOME}/.ssh/id_rsa.pub"
    fi
}

check_prerequisites() {
    if ! has_cmd incus; then
        fail "incus command not found. Please install Incus first."
        exit 1
    fi

    if ! incus info >/dev/null 2>&1; then
        fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
        exit 1
    fi

    init_ssh

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        exit 1
    fi

    slog "Using SSH key: $SSH_KEY"
}

configure_distribution() {
    case "$DISTRO" in
    ubuntu)
        RELEASE=${RELEASE:-"24.04"}
        USERNAME=${USERNAME:-"ubuntu"}
        IMAGE="images:ubuntu/${RELEASE}/cloud"
        ;;
    fedora)
        RELEASE=${RELEASE:-"42"}
        USERNAME=${USERNAME:-"fedora"}
        IMAGE="images:fedora/${RELEASE}/cloud"
        ;;
    arch)
        RELEASE=${RELEASE:-"current"}
        USERNAME=${USERNAME:-"arch"}
        IMAGE="images:archlinux/${RELEASE}/cloud"
        ;;
    debian)
        RELEASE=${RELEASE:-"12"}
        USERNAME=${USERNAME:-"debian"}
        IMAGE="images:debian/${RELEASE}/cloud"
        ;;
    centos)
        RELEASE=${RELEASE:-"9-Stream"}
        USERNAME=${USERNAME:-"centos"}
        IMAGE="images:centos/${RELEASE}/cloud"
        ;;
    alpine)
        RELEASE=${RELEASE:-"3.19"}
        USERNAME=${USERNAME:-"alpine"}
        IMAGE="images:alpine/${RELEASE}/cloud"
        ;;
    tumbleweed)
        RELEASE=${RELEASE:-"current"}
        USERNAME=${USERNAME:-"opensuse"}
        IMAGE="images:opensuse/tumbleweed/cloud"
        ;;
    *)
        fail "Unsupported distribution: $DISTRO"
        fail "Supported distributions: ubuntu, fedora, arch, debian, centos, alpine, tumbleweed"
        exit 1
        ;;
    esac

    VM_NAME=${VM_NAME:-"${DISTRO}-vm"}
    PASSWORD=${PASSWORD:-"$USERNAME"}

    slog "Configuration:"
    slog "  Distribution: $DISTRO $RELEASE"
    slog "  VM Name: $VM_NAME"
    slog "  Username: $USERNAME"
    slog "  Image: $IMAGE"
    slog "  Resources: ${VCPUS} vCPUs, ${MEMORY_MB}MB RAM, ${DISK_SIZE} disk"
    slog "  Network Bridge: $BRIDGE_IF"
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        slog "  Dotfiles: $DOTFILES_OPTIONS"
    fi
}

generate_cloud_init_config() {
    slog "Generating cloud-init configuration..."

    # Create temporary directory for cloud-init files
    CLOUD_INIT_DIR=$(mktemp -d)

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    local packages_common="qemu-guest-agent curl wget vim htop git unzip"
    local openssh_pkg

    if [[ "$DISTRO" == "arch" ]]; then
        openssh_pkg="openssh"
    else
        openssh_pkg="openssh-server"
    fi

    # Prepare MOTD content
    local motd_content="Welcome to $VM_NAME ($DISTRO $RELEASE)!
      Created with ivm-create

      SSH access configured for user: $USERNAME"

    # Prepare runcmd array
    local runcmd_lines=(
        "systemctl enable --now ssh || systemctl enable --now sshd"
        "systemctl enable --now qemu-guest-agent || true"
    )

    # Add Nix installation if specified
    if [[ "$INSTALL_NIX" == "true" ]]; then
        slog "Adding Nix installation to cloud-init"
        motd_content+="

      Nix package manager is installed and ready to use.
      Quick commands:
        nix --version
        nix search <package>
        nix-env -iA nixpkgs.<package>
        nix-shell -p <package>"

        runcmd_lines+=(
            "curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install --no-confirm"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/bash.bashrc"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/profile"
            "echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /home/<USER>/.bashrc"
            "systemctl enable --now nix-daemon || true"
        )
    fi

    # Add dotfiles installation if specified
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        slog "Adding dotfiles installation to cloud-init with options: $DOTFILES_OPTIONS"
        motd_content+="

      Dotfiles are installed and ready to use.
      Location: /home/<USER>/.ilm
      Quick commands:
        ilmi shell
        ilmi docker
        ilmi python
        ilmc zsh
        ilmc tmux
        ilmc nvim"

        runcmd_lines+=(
            "su - $USERNAME -c 'bash -c \"\$(curl -sSL https://dub.sh/aPKPT8V || wget -qO- https://dub.sh/aPKPT8V)\" -- $DOTFILES_OPTIONS'"
        )
    fi

    # Create cloud-init user-data file using tee
    tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups:
      - sudo
      - wheel
      - adm
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: $(openssl passwd -6 "$PASSWORD")
    ssh_authorized_keys:
      - "$pub_key"

# System configuration
package_update: true
package_upgrade: true

packages:
  - $openssh_pkg
$(for pkg in $packages_common; do echo "  - $pkg"; done)

# Enable SSH service and ensure proper configuration
runcmd:
$(printf "  - %s\n" "${runcmd_lines[@]}")

# Network configuration
write_files:
  - path: /etc/motd
    content: |
      $motd_content

final_message: "VM $VM_NAME setup complete! SSH access is ready."
EOF

    # Create meta-data file
    tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

    success "Cloud-init configuration files created in: $CLOUD_INIT_DIR"
}

create_vm() {
    slog "Creating Incus VM '$VM_NAME'..."

    # Check if VM already exists
    if incus info "$VM_NAME" >/dev/null 2>&1; then
        fail "VM '$VM_NAME' already exists"
        exit 1
    fi

    # Generate cloud-init configuration files
    generate_cloud_init_config

    # Create the VM with cloud-init configuration
    slog "Launching VM with image: $IMAGE"

    # Build launch command with distribution-specific settings
    local launch_cmd=(
        incus launch "$IMAGE" "$VM_NAME" --vm
        --config "limits.cpu=$VCPUS"
        --config "limits.memory=${MEMORY_MB}MB"
        --config "user.user-data=$(cat "${CLOUD_INIT_DIR}/user-data")"
        --config "user.meta-data=$(cat "${CLOUD_INIT_DIR}/meta-data")"
        --device "root,size=$DISK_SIZE"
        --network "$BRIDGE_IF"
    )

    if [[ "$DISTRO" == "arch" ]]; then
        launch_cmd+=(--config "security.secureboot=false")
    fi

    if ! "${launch_cmd[@]}"; then
        fail "Failed to create VM '$VM_NAME'"
        exit 1
    fi

    success "VM '$VM_NAME' created successfully"

    # Clean up temporary cloud-init directory
    if [[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]]; then
        rm -rf "$CLOUD_INIT_DIR"
        slog "Cleaned up temporary cloud-init files"
    fi
}

get_vm_ip() {
    slog "Getting VM IP address..."

    local ip
    local max_attempts=30
    local attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        ip=$(incus list "$VM_NAME" --format csv --columns 4 | head -1 | cut -d' ' -f1)

        if [[ -n "$ip" && "$ip" != "-" ]]; then
            echo "$ip"
            return 0
        fi

        sleep 2
        ((attempt++))
    done

    warn "Could not determine VM IP address"
    return 1
}

show_completion_info() {
    success "VM '$VM_NAME' is ready!"
    echo
    slog "VM Details:"
    incus list "$VM_NAME"
    echo

    local ip
    if ip=$(get_vm_ip); then
        slog "SSH Access:"
        slog "  IP Address: $ip"
        slog "  Username: $USERNAME"
        slog "  SSH Command: ssh $USERNAME@$ip"
        echo
        slog "You can also use Incus commands:"
        slog "  Console: incus console $VM_NAME"
        slog "  Execute: incus exec $VM_NAME -- <command>"
        slog "  Shell: incus exec $VM_NAME -- /bin/bash"
    else
        slog "Use Incus commands to access the VM:"
        slog "  Console: incus console $VM_NAME"
        slog "  Execute: incus exec $VM_NAME -- <command>"
        slog "  Shell: incus exec $VM_NAME -- /bin/bash"
    fi

    # Show Nix information if installed
    if [[ "$INSTALL_NIX" == "true" ]]; then
        echo
        slog "Nix Package Manager Information:"
        slog "  Nix is pre-installed on this VM"
        slog "  Check version:      nix --version"
        slog "  Search packages:    nix search <package>"
        slog "  Install package:    nix-env -iA nixpkgs.<package>"
        slog "  Temporary shell:    nix-shell -p <package>"
        slog "  Documentation: https://nixos.org/manual/nix/stable/"
    fi

    # Show dotfiles information if installed
    if [[ -n "$DOTFILES_OPTIONS" ]]; then
        echo
        slog "Dotfiles Information:"
        slog "  Dotfiles are pre-installed on this VM with options: $DOTFILES_OPTIONS"
        slog "  Location: ~/.ilm"
        slog "  Install shell:      ilmi shell"
        slog "  Install Docker:     ilmi docker"
        slog "  Install Python:     ilmi python"
        slog "  Config zsh:         ilmc zsh"
        slog "  Config tmux:        ilmc tmux"
        slog "  Config nvim:        ilmc nvim"
        slog "  Repository: https://github.com/pervezfunctor/dotfiles"
    fi

    echo
    slog "VM Management:"
    slog "  Status: ivm status $VM_NAME"
    slog "  Stop: ivm stop $VM_NAME"
    slog "  Start: ivm start $VM_NAME"
    slog "  Delete: ivm delete $VM_NAME"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        --distro)
            DISTRO="$2"
            shift 2
            ;;
        --name)
            VM_NAME="$2"
            shift 2
            ;;
        --release)
            RELEASE="$2"
            shift 2
            ;;
        --username)
            USERNAME="$2"
            shift 2
            ;;
        --password)
            PASSWORD="$2"
            shift 2
            ;;
        --vcpus)
            VCPUS="$2"
            shift 2
            ;;
        --memory)
            MEMORY_MB="$2"
            shift 2
            ;;
        --disk-size)
            DISK_SIZE="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --bridge)
            BRIDGE_IF="$2"
            shift 2
            ;;
        --nix)
            INSTALL_NIX=true
            shift
            ;;
        --dotfiles)
            shift                 # Remove --dotfiles from arguments
            DOTFILES_OPTIONS="$*" # Capture all remaining arguments
            break                 # Exit the loop since --dotfiles must be last
            ;;
        --help | -h)
            usage
            exit 0
            ;;
        *)
            fail "Unknown option: $1"
            usage
            exit 1
            ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$DISTRO" ]]; then
        fail "Distribution is required. Use --distro option."
        usage
        exit 1
    fi
}

main() {
    slog "Starting Incus VM creation..."

    parse_args "$@"
    check_prerequisites
    configure_distribution
    create_vm
    show_completion_info

    success "All done! Your $DISTRO VM '$VM_NAME' is ready to use."
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
