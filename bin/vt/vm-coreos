#!/bin/bash

set -euo pipefail

# Default values
VM_NAME=""
VCPUS=2
RAM_MB=2048
DISK_GB=20
BRIDGE_IF="virbr0"
SSH_KEY=""
STREAM="stable"
HOSTNAME=""
IGNITION_CONFIG=""
WORKDIR="/var/lib/libvirt/images"
USERNAME="coreos" # CoreOS default user
DOWNLOAD_IMAGE=true
FORCE_DOWNLOAD=false
INSTALL_K3S=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

# Auto-detect SSH key
if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_rsa.pub"
else
    SSH_KEY=""
fi

usage() {
    cat <<EOF
Usage: $0 --name VM_NAME [OPTIONS]

Create a Fedora CoreOS VM using virt-install and Ignition configuration.

REQUIRED:
    --name NAME         VM name

OPTIONS:
    --memory MB         RAM in MB (default: $RAM_MB)
    --vcpus NUM         Number of vCPUs (default: $VCPUS)
    --disk-size GB      Disk size in GB (default: $DISK_GB)
    --ssh-key PATH      SSH public key path (default: auto-detected)
    --bridge BRIDGE     Network bridge (default: $BRIDGE_IF)
    --stream STREAM     CoreOS stream: stable|testing|next (default: $STREAM)
    --hostname HOST     VM hostname (default: VM_NAME)
    --ignition PATH     Custom Ignition config file (optional)
    --no-download       Use existing image, don't download
    --force-download    Force re-download even if image exists
    --k3s               Install k3s Kubernetes cluster
    --help, -h          Show this help

EXAMPLES:
    $0 --name coreos-test                           # Basic CoreOS VM
    $0 --name coreos-dev --memory 4096 --vcpus 4    # Development VM
    $0 --name coreos-k3s --k3s                      # VM with k3s Kubernetes
    $0 --name coreos-custom --ignition config.ign   # VM with custom config
    $0 --name coreos-fresh --force-download         # Force re-download image

PREREQUISITES:
    - KVM/QEMU installed and running
    - libvirt daemon running
    - virt-install package installed
    - SSH key pair generated
    - coreos-installer or butane (for custom configs)

INSTALL COMMANDS:
    Fedora:        sudo dnf install qemu-kvm libvirt virt-install coreos-installer butane
    Ubuntu/Debian: sudo apt install qemu-kvm libvirt-daemon-system libvirt-clients virtinst
    Arch:          sudo pacman -S qemu-desktop libvirt virt-install

EOF
}

while [[ $# -gt 0 ]]; do
    case $1 in
    --name)
        VM_NAME="$2"
        shift 2
        ;;
    --memory)
        RAM_MB="$2"
        shift 2
        ;;
    --vcpus)
        VCPUS="$2"
        shift 2
        ;;
    --disk-size)
        DISK_GB="$2"
        shift 2
        ;;
    --ssh-key)
        SSH_KEY="$2"
        shift 2
        ;;
    --bridge)
        BRIDGE_IF="$2"
        shift 2
        ;;
    --stream)
        STREAM="$2"
        shift 2
        ;;
    --hostname)
        HOSTNAME="$2"
        shift 2
        ;;
    --ignition)
        IGNITION_CONFIG="$2"
        shift 2
        ;;
    --no-download)
        DOWNLOAD_IMAGE=false
        shift
        ;;
    --force-download)
        FORCE_DOWNLOAD=true
        shift
        ;;
    --k3s)
        INSTALL_K3S=true
        shift
        ;;
    --help | -h)
        usage
        exit 0
        ;;
    *)
        log_error "Unknown option: $1"
        usage
        exit 1
        ;;
    esac
done

if [[ -z "$VM_NAME" ]]; then
    log_error "VM name is required. Use --name VM_NAME"
    usage
    exit 1
fi

# Set default hostname to VM name if not specified
HOSTNAME="${HOSTNAME:-$VM_NAME}"

# Set up paths
VM_WORKDIR="${WORKDIR}/${VM_NAME}-coreos"
COREOS_IMAGE="${VM_WORKDIR}/fedora-coreos-${STREAM}.qcow2"
VM_DISK="${VM_WORKDIR}/${VM_NAME}.qcow2"
BUTANE_CONFIG="${VM_WORKDIR}/${VM_NAME}.bu"
GENERATED_IGNITION="${VM_WORKDIR}/${VM_NAME}.ign"

cleanup_on_error() {
    log_warn "Cleaning up due to error..."
    sudo rm -f "$VM_DISK" 2>/dev/null || true
    sudo rm -f "$GENERATED_IGNITION" 2>/dev/null || true
    sudo virsh destroy "$VM_NAME" 2>/dev/null || true
    sudo virsh undefine "$VM_NAME" 2>/dev/null || true
}

trap cleanup_on_error ERR
trap 'cleanup_on_error; exit 1' INT TERM

check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! sudo -v; then
        log_error "This script requires sudo privileges"
        exit 1
    fi

    local missing_tools=()
    for tool in virsh virt-install; do
        if ! command -v "$tool" &>/dev/null; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Install with distribution package manager"
        exit 1
    fi

    if ! systemctl is-active --quiet libvirtd; then
        log_error "libvirtd service is not running"
        log_error "Start with: sudo systemctl start libvirtd"
        exit 1
    fi

    if [[ -z "$SSH_KEY" ]] || [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH public key not found. Please specify with --ssh-key or generate one:"
        log_error "ssh-keygen -t ed25519 -C '<EMAIL>'"
        exit 1
    fi

    if virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' already exists"
        log_error "Remove with: virsh destroy $VM_NAME && virsh undefine $VM_NAME"
        exit 1
    fi

    if ! ip link show "$BRIDGE_IF" &>/dev/null; then
        log_warn "Bridge '$BRIDGE_IF' not found, will use default libvirt network"
        BRIDGE_IF="default"
    fi

    log_success "Prerequisites check passed"
}

download_coreos_image() {
    # Check if we should skip download
    if [[ "$DOWNLOAD_IMAGE" == "false" ]] && [[ -f "$COREOS_IMAGE" ]]; then
        log_info "Using existing CoreOS image: $COREOS_IMAGE"
        return 0
    fi

    # Check if image already exists and we're not forcing download
    if [[ "$DOWNLOAD_IMAGE" == "true" ]] && [[ "$FORCE_DOWNLOAD" == "false" ]] && [[ -f "$COREOS_IMAGE" ]]; then
        log_info "CoreOS image already exists: $COREOS_IMAGE"
        log_info "Use --no-download to skip download, or --force-download to re-download"
        return 0
    fi

    log_info "Setting up CoreOS image for stream: $STREAM"

    if [[ "$FORCE_DOWNLOAD" == "true" ]]; then
        log_info "Force download requested, will re-download image"
    fi

    sudo mkdir -p "$VM_WORKDIR"
    sudo chown "$USER:$USER" "$VM_WORKDIR"
    chmod 755 "$VM_WORKDIR"

    if command -v coreos-installer &>/dev/null; then
        log_info "Downloading CoreOS image using coreos-installer..."

        # Remove existing files to avoid conflicts
        rm -f "$COREOS_IMAGE"
        find "$VM_WORKDIR" -name "fedora-coreos-*.qcow2" -delete 2>/dev/null || true

        coreos-installer download -s "$STREAM" -p qemu -f qcow2.xz --decompress -C "$VM_WORKDIR/"

        # Find the downloaded image and rename it
        local downloaded_image
        downloaded_image=$(find "$VM_WORKDIR" -name "fedora-coreos-*.qcow2" | head -1)
        if [[ -n "$downloaded_image" ]]; then
            mv "$downloaded_image" "$COREOS_IMAGE"
        else
            log_error "Failed to find downloaded CoreOS image"
            exit 1
        fi
    else
        log_warn "coreos-installer not found, attempting direct download..."

        # Get the download URL from the stream metadata
        local stream_url="https://builds.coreos.fedoraproject.org/streams/${STREAM}.json"
        local download_url

        if command -v jq &>/dev/null; then
            download_url=$(curl -s "$stream_url" | jq -r '.architectures.x86_64.artifacts.qemu.formats["qcow2.xz"].disk.location')
        else
            log_error "Neither coreos-installer nor jq found. Please install one of them:"
            log_error "  Fedora: sudo dnf install coreos-installer"
            log_error "  Ubuntu: sudo apt install jq"
            exit 1
        fi

        if [[ "$download_url" == "null" ]] || [[ -z "$download_url" ]]; then
            log_error "Failed to get download URL for CoreOS $STREAM stream"
            exit 1
        fi

        log_info "Downloading from: $download_url"

        # Remove existing files if they exist
        rm -f "${COREOS_IMAGE}.xz" "$COREOS_IMAGE"

        if ! curl -L -o "${COREOS_IMAGE}.xz" "$download_url"; then
            log_error "Failed to download CoreOS image"
            exit 1
        fi

        log_info "Decompressing image..."
        if ! xz -d "${COREOS_IMAGE}.xz"; then
            log_error "Failed to decompress CoreOS image"
            log_error "You may need to remove existing files manually:"
            log_error "  rm -f '${COREOS_IMAGE}' '${COREOS_IMAGE}.xz'"
            exit 1
        fi
    fi

    if [[ ! -f "$COREOS_IMAGE" ]]; then
        log_error "CoreOS image not found after download: $COREOS_IMAGE"
        exit 1
    fi

    log_success "CoreOS image ready: $COREOS_IMAGE"
}

generate_ignition_config() {
    if [[ -n "$IGNITION_CONFIG" ]]; then
        if [[ ! -f "$IGNITION_CONFIG" ]]; then
            log_error "Custom Ignition config file not found: $IGNITION_CONFIG"
            exit 1
        fi
        log_info "Using custom Ignition config: $IGNITION_CONFIG"
        cp "$IGNITION_CONFIG" "$GENERATED_IGNITION"
        return 0
    fi

    log_info "Generating Ignition configuration..."

    local ssh_key_content
    ssh_key_content=$(cat "$SSH_KEY")

    log_info "Working directory: $VM_WORKDIR"
    log_info "Butane config will be: $BUTANE_CONFIG"
    log_info "Ignition config will be: $GENERATED_IGNITION"

    # Create Butane config
    cat >"$BUTANE_CONFIG" <<EOF
variant: fcos
version: 1.6.0
passwd:
  users:
    - name: $USERNAME
      ssh_authorized_keys:
        - $ssh_key_content
      groups:
        - wheel
        - sudo
storage:
  files:
    - path: /etc/hostname
      mode: 0644
      contents:
        inline: $HOSTNAME
EOF

    # Verify Butane config was created and set proper permissions
    if [[ ! -f "$BUTANE_CONFIG" ]]; then
        log_error "Failed to create Butane config file: $BUTANE_CONFIG"
        exit 1
    fi
    chmod 644 "$BUTANE_CONFIG"
    log_info "Butane config created successfully"

    # Add k3s installation if requested
    if [[ "$INSTALL_K3S" == "true" ]]; then
        log_info "Adding k3s Kubernetes installation to config"
        cat >>"$BUTANE_CONFIG" <<EOF
systemd:
  units:
    - name: k3s-install.service
      enabled: true
      contents: |
        [Unit]
        Description=Install k3s Kubernetes
        After=network-online.target
        Wants=network-online.target

        [Service]
        Type=oneshot
        ExecStart=/usr/bin/curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644
        ExecStartPost=/usr/bin/systemctl enable --now k3s
        RemainAfterExit=yes

        [Install]
        WantedBy=multi-user.target
    - name: k3s-setup.service
      enabled: true
      contents: |
        [Unit]
        Description=Setup k3s for user access
        After=k3s-install.service
        Requires=k3s-install.service

        [Service]
        Type=oneshot
        ExecStart=/usr/bin/mkdir -p /home/<USER>/.kube
        ExecStart=/usr/bin/cp /etc/rancher/k3s/k3s.yaml /home/<USER>/.kube/config
        ExecStart=/usr/bin/chown -R $USERNAME:$USERNAME /home/<USER>/.kube
        ExecStart=/usr/bin/chmod 600 /home/<USER>/.kube/config
        RemainAfterExit=yes

        [Install]
        WantedBy=multi-user.target
EOF
    fi

    # Convert Butane config to Ignition
    if command -v butane &>/dev/null; then
        log_info "Converting Butane config to Ignition using butane..."
        if ! butane --pretty --strict "$BUTANE_CONFIG" >"$GENERATED_IGNITION"; then
            log_error "Failed to convert Butane config to Ignition"
            exit 1
        fi
    elif command -v podman &>/dev/null; then
        log_info "Converting Butane config to Ignition using podman..."
        # Try using stdin method first (more reliable with permissions)
        if ! podman run --rm -i quay.io/coreos/butane:release --pretty --strict <"$BUTANE_CONFIG" >"$GENERATED_IGNITION"; then
            log_warn "Stdin method failed, trying volume mount method..."
            # Fallback to volume mount with SELinux disabled
            if ! podman run --rm -i --security-opt label=disable \
                --volume "${VM_WORKDIR}:/pwd" --workdir /pwd \
                quay.io/coreos/butane:release --pretty --strict "${VM_NAME}.bu" >"$GENERATED_IGNITION"; then
                log_error "Failed to convert Butane config to Ignition using podman"
                exit 1
            fi
        fi
    elif command -v docker &>/dev/null; then
        log_info "Converting Butane config to Ignition using docker..."
        # Try using stdin method first (more reliable with permissions)
        if ! docker run --rm -i quay.io/coreos/butane:release --pretty --strict <"$BUTANE_CONFIG" >"$GENERATED_IGNITION"; then
            log_warn "Stdin method failed, trying volume mount method..."
            # Fallback to volume mount
            if ! docker run --rm -i --volume "${VM_WORKDIR}:/pwd" --workdir /pwd \
                quay.io/coreos/butane:release --pretty --strict "${VM_NAME}.bu" >"$GENERATED_IGNITION"; then
                log_error "Failed to convert Butane config to Ignition using docker"
                exit 1
            fi
        fi
    else
        log_error "No tool found to convert Butane config. Please install one of:"
        log_error "  - butane (recommended): sudo dnf install butane"
        log_error "  - podman: sudo dnf install podman"
        log_error "  - docker: sudo dnf install docker"
        exit 1
    fi

    if [[ ! -f "$GENERATED_IGNITION" ]]; then
        log_error "Failed to generate Ignition config"
        exit 1
    fi

    log_success "Ignition configuration generated: $GENERATED_IGNITION"
}

create_vm_disk() {
    log_info "Creating VM disk..."

    if ! cp "$COREOS_IMAGE" "$VM_DISK"; then
        log_error "Failed to copy CoreOS image"
        exit 1
    fi

    if ! qemu-img resize "$VM_DISK" "${DISK_GB}G"; then
        log_error "Failed to resize VM disk"
        exit 1
    fi

    log_success "VM disk created: $VM_DISK (${DISK_GB}G)"
}

launch_vm() {
    log_info "Launching Fedora CoreOS VM '$VM_NAME'..."

    # Set up SELinux context for Ignition file
    if command -v chcon &>/dev/null; then
        chcon --verbose --type svirt_home_t "$GENERATED_IGNITION" 2>/dev/null || true
    fi

    local network_config
    if [[ "$BRIDGE_IF" == "default" ]]; then
        network_config="network=default,model=virtio"
    else
        network_config="bridge=$BRIDGE_IF,model=virtio"
    fi

    # Determine architecture-specific Ignition device arguments
    local ignition_device_args=()
    local arch
    arch=$(uname -m)

    case "$arch" in
    x86_64 | aarch64)
        ignition_device_args=("--qemu-commandline=-fw_cfg name=opt/com.coreos/config,file=${GENERATED_IGNITION}")
        ;;
    s390x | ppc64le)
        ignition_device_args=("--disk" "path=${GENERATED_IGNITION},format=raw,readonly=on,serial=ignition,startup_policy=optional")
        ;;
    *)
        log_warn "Unknown architecture $arch, using x86_64 method"
        ignition_device_args=("--qemu-commandline=-fw_cfg name=opt/com.coreos/config,file=${GENERATED_IGNITION}")
        ;;
    esac

    log_info "VM Configuration:"
    log_info "  Name: $VM_NAME"
    log_info "  Hostname: $HOSTNAME"
    log_info "  Stream: $STREAM"
    log_info "  Memory: ${RAM_MB}MB"
    log_info "  vCPUs: $VCPUS"
    log_info "  Disk: $VM_DISK (${DISK_GB}G)"
    log_info "  Network: $network_config"
    log_info "  Ignition: $GENERATED_IGNITION"

    if ! virt-install \
        --connect="qemu:///system" \
        --name="$VM_NAME" \
        --vcpus="$VCPUS" \
        --memory="$RAM_MB" \
        --os-variant="fedora-coreos-$STREAM" \
        --import \
        --graphics=none \
        --disk="size=${DISK_GB},backing_store=${COREOS_IMAGE}" \
        --network="$network_config" \
        "${ignition_device_args[@]}" \
        --noautoconsole; then
        log_error "Failed to create VM"
        exit 1
    fi

    if ! virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' was not created successfully"
        exit 1
    fi

    log_success "VM '$VM_NAME' created successfully!"

    # Add VM to /etc/hosts if vm-hosts script is available
    log_info "Adding VM to /etc/hosts for name-based access..."
    sleep 5
    if [[ -x "$HOME/.ilm/bin/vm-hosts" ]]; then
        "$HOME/.ilm/bin/vm-hosts" add "$VM_NAME" || log_warn "Could not add VM to /etc/hosts automatically"
    elif [[ -x "$(command -v vm-hosts)" ]]; then
        vm-hosts add "$VM_NAME" || log_warn "Could not add VM to /etc/hosts automatically"
    else
        log_warn "vm-hosts script not found, skipping /etc/hosts update"
    fi
}

show_completion_info() {
    log_success "=== Fedora CoreOS VM Creation Complete ==="
    echo
    log_info "VM Details:"
    echo "  Name: $VM_NAME"
    echo "  Hostname: $HOSTNAME"
    echo "  Stream: $STREAM"
    echo "  Memory: ${RAM_MB}MB"
    echo "  vCPUs: $VCPUS"
    echo "  Disk: ${DISK_GB}G"
    echo "  Username: $USERNAME"
    echo "  SSH Key: $SSH_KEY"
    echo

    if [[ "$INSTALL_K3S" == "true" ]]; then
        log_info "k3s Kubernetes will be installed on first boot"
        echo "  Note: k3s installation takes 2-3 minutes after Ignition completes"
        echo "  k3s Commands (after installation):"
        echo "    Check status:       sudo systemctl status k3s"
        echo "    View nodes:         kubectl get nodes"
        echo "    View pods:          kubectl get pods -A"
        echo "    Access dashboard:   kubectl proxy"
        echo "    Kubeconfig:         ~/.kube/config"
        echo
    fi

    log_info "Useful Commands:"
    echo "  Check VM status:    virsh list --all"
    echo "  Start VM:           virsh start $VM_NAME"
    echo "  Stop VM:            virsh shutdown $VM_NAME"
    echo "  Force stop VM:      virsh destroy $VM_NAME"
    echo "  Delete VM:          virsh undefine $VM_NAME"
    echo "  Console access:     virsh console $VM_NAME"
    echo "  Get VM IP:          virsh domifaddr $VM_NAME"
    echo
    log_info "SSH Access:"
    echo "  Wait 2-3 minutes for Ignition to complete"
    echo "  Find VM IP:         virsh domifaddr $VM_NAME"
    echo "  SSH by name:        ssh $USERNAME@$VM_NAME"
    echo "  SSH by IP:          ssh $USERNAME@<VM_IP>"
    echo
    log_info "CoreOS Commands:"
    echo "  Update system:      rpm-ostree upgrade"
    echo "  Install packages:   rpm-ostree install <package>"
    echo "  Reboot to apply:    systemctl reboot"
    echo "  Check status:       rpm-ostree status"
    echo "  Rollback:           rpm-ostree rollback"
    echo
    log_info "Container Commands (Podman is pre-installed):"
    echo "  Run container:      podman run <image>"
    echo "  List containers:    podman ps"
    echo "  List images:        podman images"
    echo "  Pull image:         podman pull <image>"
    echo

    if [[ "$INSTALL_K3S" == "true" ]]; then
        log_info "k3s Kubernetes Commands:"
        echo "  Check cluster:      kubectl cluster-info"
        echo "  Get nodes:          kubectl get nodes"
        echo "  Get all pods:       kubectl get pods -A"
        echo "  Get services:       kubectl get svc -A"
        echo "  Apply manifest:     kubectl apply -f <file>"
        echo "  Delete resource:    kubectl delete <type> <name>"
        echo "  View logs:          kubectl logs <pod>"
        echo "  Exec into pod:      kubectl exec -it <pod> -- /bin/sh"
        echo "  Port forward:       kubectl port-forward <pod> <local>:<remote>"
        echo
    fi

    echo
    if [[ "$INSTALL_K3S" == "true" ]]; then
        log_warn "Note: Ignition runs first (2-3 min), then k3s installs (2-3 min)."
    else
        log_warn "Note: Ignition configuration runs on first boot (2-3 minutes)."
    fi
}

main() {
    log_info "Starting Fedora CoreOS VM creation..."
    log_info "VM Name: $VM_NAME"
    log_info "Stream: $STREAM"
    log_info "Working Directory: $VM_WORKDIR"

    check_prerequisites
    download_coreos_image
    generate_ignition_config
    create_vm_disk
    launch_vm
    show_completion_info

    log_success "All done! Your Fedora CoreOS VM is ready."
}

main "$@"
