#!/bin/bash

set -euo pipefail

# shellcheck disable=SC1090
source ~/.ilm/share/utils

usage() {
    cat <<EOF
Usage: $0 <command> [container-name] [args...]

Manage distrobox containers similar to VM management.

COMMANDS:
    install                 Install container tools (distrobox, podman, etc.) using ilmi
    list                    List all distrobox containers
    status <name>           Show container status and info
    create <distro> [name]  Create a new distrobox container
    start <name>            Start a container
    stop <name>             Stop a container
    restart <name>          Restart a container
    delete <name>           Delete a container completely
    enter <name>            Enter container shell
    run <name> <cmd>        Run command in container
    export <name> <app>     Export application from container
    unexport <app>          Unexport application
    upgrade <name>          Upgrade container packages
    logs <name>             Show container logs
    logs-tail <name>        Follow container logs (like tail -f)
    cleanup                 Remove stopped containers and orphaned files

SUPPORTED DISTROS:
    ubuntu, debian, arch, fedora, rocky, tumbleweed, alpine,
    bluefin, docker, wolfi, nix

EXAMPLES:
    $0 install                      # Install container tools
    $0 list                         # List all containers
    $0 status ubuntu                # Show status of 'ubuntu' container
    $0 create ubuntu myubuntu       # Create Ubuntu container named 'myubuntu'
    $0 create fedora                # Create Fedora container with default name
    $0 enter ubuntu                 # Enter 'ubuntu' container
    $0 run ubuntu "ls -la"          # Run command in 'ubuntu' container
    $0 export ubuntu firefox        # Export firefox from ubuntu container
    $0 logs ubuntu                  # Show logs for 'ubuntu' container
    $0 logs-tail ubuntu             # Follow logs for 'ubuntu' container
    $0 delete old-container         # Delete 'old-container' completely

EOF
}

has_container() {
    local container_name="$1"
    distrobox list | grep -q "\b${container_name}\b" || return 1
}

check_container() {
    local container_name="$1"

    if ! has_container "$container_name"; then
        fail "Container '$container_name' not found"
        exit 1
    fi
}

list_containers() {
    slog "Listing all distrobox containers..."

    echo
    distrobox list

    if has_cmd podman; then
        echo
        echo
        echo
        slog "Podman container details:"
        podman ps -a --filter "label=manager=distrobox" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedHuman}}" 2>/dev/null || true
    elif has_cmd docker; then
        echo
        echo
        echo
        slog "Docker container details:"
        docker ps -a --filter "label=manager=distrobox" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null || true
    fi
}

install_distrobox() {
    slog "Installing distrobox using ilmi..."

    has_cmd ilmi || err_exit "ilmi not found. Quitting"

    ilmi ct

    if has_cmd distrobox; then
        success "Container tools installed successfully!"
        echo
        slog "Installed tools include:"
        has_cmd distrobox && slog "  ✓ distrobox"
        has_cmd podman && slog "  ✓ podman"
        has_cmd docker && slog "  ✓ docker"
        has_cmd incus && slog "  ✓ incus"
        echo
        slog "You can now use:"
        slog "  $0 list                    # List containers"
        slog "  $0 create ubuntu           # Create Ubuntu distrobox container"
        slog "  $0 enter ubuntu            # Enter distrobox container"
        echo

        if ! has_cmd podman && ! has_cmd docker; then
            warn "Neither Podman nor Docker found. Distrobox requires one of them."
            slog "The installation should have included Podman. Try logging out and back in."
        fi
    else
        fail "Container tools installation failed. Please check the output above for errors."
        return 1
    fi
}

container_status() {
    local container_name="$1"

    check_container "$container_name"

    slog "Status for container '$container_name':"

    local engine
    engine=$(distrobox list | grep "\b${container_name}\b" | awk '{print $NF}' | head -1)
    if [[ -n "$engine" ]]; then
        slog "Container engine details:"
        if has_cmd podman; then
            podman ps -a --filter "name=${container_name}" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedHuman}}" 2>/dev/null || true
        elif has_cmd docker; then
            docker ps -a --filter "name=${container_name}" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null || true
        fi
    fi
}

create_container() {
    local distro="$1"
    local container_name="${2:-$distro}"

    if has_container "$container_name"; then
        fail "Container '$container_name' already exists"
        return 1
    fi

    slog "Creating $distro distrobox container: $container_name"

    case "$distro" in
    ubuntu)
        distrobox create --yes --image ubuntu:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    debian)
        distrobox create --yes --image debian:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    arch)
        distrobox create --yes --image archlinux:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    fedora)
        distrobox create --yes --image fedora:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    rocky)
        distrobox create --yes --image quay.io/rockylinux/rockylinux:9 --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    tumbleweed | tw)
        distrobox create --yes --image opensuse/tumbleweed --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    alpine)
        distrobox create --yes --image quay.io/toolbx-images/alpine-toolbox:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    bluefin)
        distrobox create --yes --image ghcr.io/ublue-os/bluefin-cli:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    docker)
        distrobox create --yes --image ghcr.io/ublue-os/docker-distrobox:latest --init --unshare-all --root --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    wolfi)
        distrobox create --yes --image ghcr.io/wolfi-dev/sdk:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    nix)
        distrobox create --yes --image nixos/nix:latest --home "$HOME/.boxes/${container_name}" --name "${container_name}"
        ;;
    *)
        fail "Unsupported distro: $distro"
        slog "Supported distros: ubuntu, debian, arch, fedora, rocky, tumbleweed, alpine, bluefin, docker, wolfi, nix"
        return 1
        ;;
    esac

    # shellcheck disable=SC2181
    if [[ $? -eq 0 ]]; then
        success "Container '$container_name' created successfully"
    else
        fail "Failed to create container '$container_name'"
        return 1
    fi
}

start_container() {
    local container_name="$1"

    check_container "$container_name"

    slog "Starting container '$container_name'..."

    # Distrobox containers are typically started when entered
    # But we can check if the underlying podman/docker container is running
    local container_id
    if has_cmd podman; then
        container_id=$(podman ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            podman start "$container_id" >/dev/null 2>&1 || true
        fi
    elif has_cmd docker; then
        container_id=$(docker ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            docker start "$container_id" >/dev/null 2>&1 || true
        fi
    fi

    success "Container '$container_name' started (will be fully initialized on first enter)"
}

stop_container() {
    local container_name="$1"

    check_container "$container_name"

    slog "Stopping container '$container_name'..."

    if distrobox stop "$container_name"; then
        success "Container '$container_name' stopped"
    else
        fail "Failed to stop container '$container_name'"
        return 1
    fi
}

restart_container() {
    local container_name="$1"
    check_container "$container_name"

    slog "Restarting container '$container_name'..."
    stop_container "$container_name"

    while distrobox list | grep -q "\b${container_name}\b"; do
        sleep 1
    done

    start_container "$container_name"
}

delete_container() {
    local container_name="$1"

    check_container "$container_name"

    warn "This will permanently delete container '$container_name' and all its data!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Deletion cancelled"
        return 0
    fi

    slog "Deleting container '$container_name'..."
    if distrobox rm --force "$container_name"; then
        success "Container '$container_name' deleted"

        # Clean up home directory if it exists
        local home_dir="$HOME/.boxes/${container_name}"
        if [[ -d "$home_dir" ]]; then
            slog "Removing container home directory: $home_dir"
            rm -rf "$home_dir"
        fi
    else
        fail "Failed to delete container '$container_name'"
        return 1
    fi
}

enter_container() {
    local container_name="$1"

    check_container "$container_name"

    slog "Entering container '$container_name'..."
    distrobox enter -nw --clean-path --name "$container_name"
}

run_in_container() {
    local container_name="$1"
    shift
    local command="$*"

    check_container "$container_name"

    slog "Running command in container '$container_name': $command"
    distrobox enter -nw --clean-path --name "$container_name" -- "$@"
}

export_app() {
    local container_name="$1"
    local app_name="$2"

    check_container "$container_name"

    slog "Exporting '$app_name' from container '$container_name'..."
    if distrobox-export --app "$app_name" --container "$container_name"; then
        success "Application '$app_name' exported successfully"
    else
        fail "Failed to export application '$app_name'"
        return 1
    fi
}

unexport_app() {
    local app_name="$1"

    slog "Unexporting application '$app_name'..."
    if distrobox-export --app "$app_name" --delete; then
        success "Application '$app_name' unexported successfully"
    else
        fail "Failed to unexport application '$app_name'"
        return 1
    fi
}

upgrade_container() {
    local container_name="$1"

    check_container "$container_name"

    slog "Upgrading packages in container '$container_name'..."
    if distrobox upgrade "$container_name"; then
        success "Container '$container_name' upgraded successfully"
    else
        fail "Failed to upgrade container '$container_name'"
        return 1
    fi
}

show_logs() {
    local container_name="$1"

    check_container "$container_name"

    slog "Showing logs for container '$container_name'..."
    echo

    # Get container ID and show logs
    local container_id
    if has_cmd podman; then
        container_id=$(podman ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            podman logs "$container_id" 2>/dev/null || warn "No logs available"
        fi
    elif has_cmd docker; then
        container_id=$(docker ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            docker logs "$container_id" 2>/dev/null || warn "No logs available"
        fi
    else
        warn "No container engine (podman/docker) found"
    fi
}

tail_logs() {
    local container_name="$1"

    check_container "$container_name"

    slog "Following logs for container '$container_name' (Press Ctrl+C to stop)..."
    echo

    # Get container ID and follow logs
    local container_id
    if has_cmd podman; then
        container_id=$(podman ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            podman logs -f "$container_id" 2>/dev/null || warn "No logs available or container not running"
        else
            warn "Container not found in podman"
        fi
    elif has_cmd docker; then
        container_id=$(docker ps -a --filter "name=${container_name}" --format "{{.ID}}" | head -1)
        if [[ -n "$container_id" ]]; then
            docker logs -f "$container_id" 2>/dev/null || warn "No logs available or container not running"
        else
            warn "Container not found in docker"
        fi
    else
        warn "No container engine (podman/docker) found"
    fi
}

cleanup_containers() {
    slog "Cleaning up stopped containers and orphaned files..."

    # List stopped containers
    local stopped_containers
    if has_cmd podman; then
        stopped_containers=$(podman ps -a --filter "status=exited" --filter "label=manager=distrobox" --format "{{.Names}}")
    elif has_cmd docker; then
        stopped_containers=$(docker ps -a --filter "status=exited" --filter "label=manager=distrobox" --format "{{.Names}}")
    fi

    if [[ -n "$stopped_containers" ]]; then
        slog "Stopped distrobox containers found:"
        echo "$stopped_containers"
        echo

        read -p "Remove all stopped distrobox containers? (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            while IFS= read -r container; do
                if [[ -n "$container" ]]; then
                    slog "Removing stopped container: $container"
                    distrobox rm --force "$container" 2>/dev/null || true
                fi
            done <<<"$stopped_containers"
        fi
    else
        slog "No stopped distrobox containers found"
    fi

    # Clean up orphaned home directories
    slog "Checking for orphaned container home directories..."
    if [[ -d "$HOME/.boxes" ]]; then
        for dir in "$HOME/.boxes"/*; do
            if [[ -d "$dir" ]]; then
                local container_name
                container_name=$(basename "$dir")
                if ! distrobox list | grep -q "\b${container_name}\b"; then
                    warn "Found orphaned directory: $dir"
                    read -p "Remove orphaned directory $dir? (y/N): " -r
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        rm -rf "$dir"
                        success "Removed: $dir"
                    fi
                fi
            fi
        done
    fi

    success "Cleanup complete"
}

# Main command handling
if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
container_name="${2:-}"

if [[ "$command" != "install" ]]; then
    if ! has_cmd distrobox; then
        fail "distrobox command not found. Please install distrobox first."
        slog "You can install it with: $0 install"
        exit 1
    fi
fi

case "$command" in
install)
    install_distrobox
    ;;
list)
    list_containers
    ;;
status)
    container_status "$container_name"
    ;;
create)
    create_container "${2:-}"
    ;;
start)
    start_container "${2:-}"
    ;;
stop)
    stop_container "$container_name"
    ;;
restart)
    restart_container "$container_name"
    ;;
delete)
    delete_container "$container_name"
    ;;
enter)
    enter_container "$container_name"
    ;;
run)
    [[ $# -lt 3 ]] && {
        fail "Command required"
        usage
        exit 1
    }
    run_in_container "$container_name" "${@:3}"
    ;;
export)
    [[ -z "${3:-}" ]] && {
        fail "Application name required"
        usage
        exit 1
    }
    export_app "$container_name" "$3"
    ;;
unexport)
    unexport_app "$container_name"
    ;;
upgrade)
    upgrade_container "$container_name"
    ;;
logs)
    show_logs "$container_name"
    ;;
logs-tail)
    tail_logs "$container_name"
    ;;
cleanup)
    cleanup_containers
    ;;
--help | -h)
    usage
    ;;
*)
    fail "Unknown command: $command"
    usage
    exit 1
    ;;
esac
