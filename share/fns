#! /usr/bin/env bash

# shellcheck disable=SC2120

dbox-ubuntu() {
    local CONTAINER_NAME=${1:-ubuntu}
    slog "Creating Ubuntu distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image ubuntu:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Ubuntu distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-arch() {
    local CONTAINER_NAME=${1:-arch}
    slog "Creating Arch distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image archlinux:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Arch distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-fedora-minimal() {
    local CONTAINER_NAME=${1:-fedora-minimal}
    slog "Creating Fedora minimal distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image registry.fedoraproject.org/fedora-minimal:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Fedora minimal distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-debian-slim() {
    local CONTAINER_NAME=${1:-debian-slim}
    slog "Creating Debian slim distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image debian:booksworm-slim --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Debian slim distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-fedora() {
    local CONTAINER_NAME=${1:-fedora}
    slog "Creating Fedora distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image fedora:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Fedora distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-centos() {
    local CONTAINER_NAME=${1:-centos}
    slog "Creating CentOS distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image centos:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "CentOS distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-debian() {
    local CONTAINER_NAME=${1:-debian}
    slog "Creating Debian distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image debian:latest --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Debian distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-rocky() {
    local CONTAINER_NAME=${1:-rocky}
    slog "Creating Rocky Linux distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image quay.io/rockylinux/rockylinux:9 --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Rocky Linux distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-tw() {
    local CONTAINER_NAME=${1:-tw}
    slog "Creating OpenSUSE Tumbleweed distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image opensuse/tumbleweed --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "OpenSUSE Tumbleweed distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-bluefin() {
    local CONTAINER_NAME=${1:-bluefin-cli}
    slog "Creating Bluefin distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image ghcr.io/ublue-os/bluefin-cli --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Bluefin distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-wolfi() {
    local CONTAINER_NAME=${1:-wolfi-ublue}
    slog "Creating Wolfi distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image ghcr.io/ublue-os/wolfi-toolbox --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Wolfi distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-docker() {
    local CONTAINER_NAME=${1:-docker}
    slog "Creating Docker distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image ghcr.io/ublue-os/docker-toolbox:latest --init --additional-packages "systemd libpam-systemd pipewire-audio-client-libraries" --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Docker distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-incus() {
    local CONTAINER_NAME=${1:-incus}
    slog "Creating Incus distrobox: ${CONTAINER_NAME}"
    if distrobox create --yes --image ghcr.io/ublue-os/incus-toolbox:latest --init --additional-packages "systemd libpam-systemd pipewire-audio-client-libraries" --home "$HOME/boxes/${CONTAINER_NAME}" --name "${CONTAINER_NAME}"; then
        slog "Incus distrobox ${CONTAINER_NAME} created successfully"
    fi
}

dbox-ublue-all() {
    slog "Creating Bluefin, Wolfi and Ublue distroboxes"
    dbox-bluefin
    dbox-wolfi
    dbox-docker
    dbox-incus

    local success=true

    if distrobox create --yes --image ghcr.io/ublue-os/ubuntu-toolbox --init --additional-packages "systemd libpam-systemd pipewire-audio-client-libraries" --home "$HOME/boxes/ubuntu-ublue" --name ubuntu-ublue; then
        slog "Ubuntu-ublue distrobox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image ghcr.io/ublue-os/fedora-toolbox --init --additional-packages "systemd" --home "$HOME/boxes/fedora-ublue" --name fedora-ublue; then
        slog "Fedora-ublue distrobox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image ghcr.io/ublue-os/arch-distrobox --init --additional-packages "systemd" --home "$HOME/boxes/arch-ublue" --name arch-ublue; then
        slog "Arch-ublue distrobox created successfully"
    else
        success=false
    fi

    if $success; then
        slog "Creating Bluefin, Wolfi and Ublue distroboxes done!"
    fi
}

dbox-toolbox-all() {
    slog "Creating toolbox distroboxes"

    local success=true

    if distrobox create --yes --image ubuntu-toolbox:latest --home "$HOME/boxes/ubuntu-toolbox" --name ubuntu-toolbox; then
        slog "Ubuntu toolbox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image fedora-toolbox:latest --home "$HOME/boxes/fedora-toolbox" --name fedora-toolbox; then
        slog "Fedora toolbox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image arch-toolbox:latest --home "$HOME/boxes/arch-toolbox" --name arch-toolbox; then
        slog "Arch toolbox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image centos-toolbox:latest --home "$HOME/boxes/centos-toolbox" --name centos-toolbox; then
        slog "CentOS toolbox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image debian-toolbox:latest --home "$HOME/boxes/debian-toolbox" --name debian-toolbox; then
        slog "Debian toolbox created successfully"
    else
        success=false
    fi

    if distrobox create --yes --image rockylinux-toolbox:9 --home "$HOME/boxes/rockylinux-toolbox" --name rockylinux-toolbox; then
        slog "Rocky Linux toolbox created successfully"
    else
        success=false
    fi

    if $success; then
        slog "Creating toolbox distroboxes done!"
    fi
}

dbox-apline-edge-init() {
    local CONTAINER_NAME=${1:-alpine-edge-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image quay.io/toolbx-images/alpine-toolbox:edge --init --additional-packages "openrc" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-apline-init() {
    local CONTAINER_NAME=${1:-alpine-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image quay.io/toolbx-images/alpine-toolbox:latest --init --additional-packages "openrc" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-debian-init() {
    local CONTAINER_NAME=${1:-debian-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image debian:stable --init --additional-packages "systemd libpam-systemd pipewire-audio-client-libraries" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-ubuntu-init() {
    local CONTAINER_NAME=${1:-ubuntu-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image ubuntu:latest --init --additional-packages "systemd libpam-systemd pipewire-audio-client-libraries" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-arch-init() {
    local CONTAINER_NAME=${1:-arch-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image archlinux:latest --init --additional-packages "systemd" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-tw-init() {
    local CONTAINER_NAME=${1:-tw-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image registry.opensuse.org/opensuse/tumbleweed:latest --init --additional-packages "systemd" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-fedora-init() {
    local CONTAINER_NAME=${1:-fedora-init}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image registry.fedoraproject.org/fedora:latest --init --additional-packages "systemd" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-alpine() {
    local CONTAINER_NAME=${1:-alpine}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image quay.io/toolbx-images/alpine-toolbox:latest --additional-packages "gcc libc-dev make gzip zsh git curl neovim tmux ripgrep luarocks fzf eza zoxide github-cli delta bat trash-cli" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        distrobox enter -nw --clean-path --name "$CONTAINER_NAME"
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-alpine-edge() {
    local CONTAINER_NAME=${1:-alpine-edge}
    slog "Creating distrobox $CONTAINER_NAME"

    if distrobox create --yes --image quay.io/toolbx-images/alpine-toolbox:edge --additional-packages "gcc libc-dev make gzip zsh git curl neovim tmux ripgrep luarocks fzf eza zoxide github-cli delta bat trash-cli" --name "$CONTAINER_NAME" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        distrobox enter -nw --clean-path --name "$CONTAINER_NAME"
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

tbox-alpine() {
    local CONTAINER_NAME=${1:-alpine}
    slog "Creating distrobox $CONTAINER_NAME"

    if toolbox create --assumeyes --image quay.io/toolbx-images/alpine-toolbox:latest "$CONTAINER_NAME"; then
        toolbox enter "$CONTAINER_NAME"
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-with-init() {
    slog "Creating distroboxes with init"

    local success=true

    dbox-ubuntu-init || success=false
    dbox-debian-init || success=false
    dbox-arch-init || success=false
    dbox-tw-init || success=false
    dbox-fedora-init || success=false
    dbox-alpine || success=false

    if $success; then
        slog "Done creating distroboxes with init"
    fi
}

toolbox-create-all() {
    slog "Creating toolbox containers"

    local success=true

    is_arch || toolbox create --assumeyes --distro arch arch || success=false
    is_fedora || toolbox create --assumeyes --distro fedora fedora || success=false
    is_ubuntu || toolbox create --assumeyes --distro ubuntu --release 24.10 ubuntu || success=false
    # toolbox create --distro rhel --release 9.5 rhel

    is_suse || toolbox create --assumeyes --image registry.opensuse.org/opensuse/toolbox:latest tw || success=false
    is_rocky || toolbox create --assumeyes --image quay.io/rockylinux/rockylinux:9 rocky || success=false
    # is_centos || toolbox create --image quay.io/toolbx-images/centos-toolbox:latest centos

    toolbox create || success=false

    if $success; then
        slog "Done creating toolbox containers"
    fi
}

function safe_push() {
    git stash -u && git push "$@" && git stash pop
}

one-shell-tmux() {
    local tmux_session="default"

    [[ -n "$TMUX" || -n "$EMACS" || -n "$INSIDE_EMACS" || -n "$VIM" || -n "$VSCODE_RESOLVING_ENVIRONMENT" || "$TERM_PROGRAM" == "vscode" ]] && return

    tmux start-server

    # Check and create session if not exists
    if ! tmux has-session -t "$tmux_session" 2>/dev/null; then
        tmux new-session -d -s "$tmux_session"
    fi

    exec tmux attach-session -t "$tmux_session"
}

dbox-nvidia-container-toolkit() {
    if has_cmd podman; then
        if distrobox create --yes --name example-nvidia-toolkit --additional-flags "--gpus all" --image docker.io/nvidia/cuda; then
            slog "NVIDIA container toolkit with podman created successfully"
        fi
    elif has_cmd docker; then
        if distrobox create --yes --name example-nvidia-toolkit --additional-flags "--gpus all --device=nvidia.com/gpu=all" --image docker.io/nvidia/cuda; then
            slog "NVIDIA container toolkit with docker created successfully"
        fi
    else
        warn "podman or docker not found"
    fi
}

dbox-to-image() {
    if has_cmd podman; then
        podman container commit -p dbox_name "$1"
        podman save "$1":latest | bzip2 >"$1".tar.bz
    elif has_cmd docker; then
        docker container commit -p dbox_name "$1"
        docker save "${1}:latest" | gzip >"$1".tar.gz
    fi
}

dbox-from-image() {
    local dbox_name=${1:dbox}
    slog "Creating distrobox from image $1"

    if distrobox create --yes --image "$1":latest --name "$dbox_name"; then
        distrobox enter -nw --clean-path --name "$dbox_name"
        slog "Done creating distrobox from image $1"
    fi
}

dbox-nix() {
    local CONTAINER_NAME=${1:-deb-nix}

    slog "Creating distrobox $CONTAINER_NAME"

    srm "$HOME/boxes/${CONTAINER_NAME}"

    if distrobox create --yes --name "${CONTAINER_NAME}" -i debian:stable --init --additional-packages "systemd libpam-systemd" --home "$HOME/boxes/${CONTAINER_NAME}"; then
        distrobox enter -nw --clean-path --name "${CONTAINER_NAME}" -- bash -c "$(curl -sSL https://dub.sh/aPKPT8V || wget -qO- https://dub.sh/aPKPT8V)" -- nixbox
        slog "Done creating distrobox $CONTAINER_NAME"
    fi
}

dbox-sizes() {
    local name="$1"

    if ! command -v distrobox >/dev/null; then
        echo "distrobox not found" >&2
        return 1
    fi

    local container="distrobox-$name"

    if command -v podman >/dev/null; then
        podman container inspect "$container" --size --format '{{.SizeRootFs}}' 2>/dev/null ||
            echo "Container '$container' not found in Podman"
    elif command -v docker >/dev/null; then
        docker container inspect "$container" --size --format '{{.SizeRootFs}}' 2>/dev/null ||
            echo "Container '$container' not found in Docker"
    else
        echo "Neither podman nor docker found" >&2
        return 1
    fi
}

incus-ubuntu-lxc() {
    incus launch images:ubuntu/24.04 ubuntu # --config limits.cpu=1 --config limits.memory=192MiB
}

incus-fedora-lxc() {
    incus launch images:fedora/42 fedora
}

incus-tw-lxc() {
    incus launch images:opensuse/tumbleweed tw
}

incus-arch-lxc() {
    incus launch images:archlinux/current archlinux
}

incus-containers() {
    slog "Creating incus containers"

    incus-ubuntu-lxc
    incus-fedora-lxc
    incus-tw-lxc
    incus-arch-lxc

    slog "Creating incus containers done!"
}

incus-ubuntu-vm() {
    incus launch images:ubuntu/24.04 ubuntu-vm --vm
}

incus-fedora-vm() {
    incus launch images:fedora/42 fedora-vm --vm
}

incus-tw-vm() {
    incus launch images:opensuse/tumbleweed-vm tw --vm
}

incus-arch-vm() {
    incus launch images:archlinux/current archlinux-vm --vm
}

incus-vms() {
    slog "Creating incus vms"

    incus-ubuntu-vm
    incus-fedora-vm
    incus-tw-vm
    incus-arch-vm

    slog "Creating incus vms done!"
}

system-update() {
    if has_cmd sup; then
        sup
    fi

    if has_cmd flatpak; then
        flatpak update --user -y
        flatpak update -y
    fi

    has_cmd snap && snap refresh
    has_cmd brew && brew upgrade
    has_cmd mise && mise self-update && mise upgrade --bump
    has_cmd pixi && pixi global update

    cd "$DOT_DIR" && git-up
    has_cmd devbox && devbox update
    has_cmd home-manager && hms
}

remove_keyrings() {
    trash .local/share/keyrings/*
}
